# AGENT.md

## Commands
- `npm start` - Start Expo development server
- `npm run typecheck` - Run TypeScript type checking (**MUST RUN after every code change**)
- `npm run lint` - Run ESLint (use `expo lint`)
- `npm run ios` - Run on iOS simulator
- `npm run android` - Run on Android emulator
- `npm run web` - Run web version
- **No test framework configured** - Consider setting up Jest and React Native Testing Library

## Architecture
- **Expo Router** with file-based routing (`app/` directory)
- **React Native** medical record management app for AI MOVE
- **API**: Real backend integration with `/v1/` endpoints for auth, cards, files, AI chat
- **State**: React Query for server state, React Context for auth/i18n
- **Navigation**: Stack navigation (home → card detail), tabs only in card detail view
- **Key features**: Voice recording, speech-to-text, AI assistant, file analysis, SOAP notes

## Code Style
- **TypeScript**: Strict mode enabled, path alias `@/*` maps to root
- **Imports**: Use `@/` alias for all internal imports
- **Components**: Theme-aware with `ThemedText`/`ThemedView`, prefer CSS boxShadow over RN shadow props
- **Styling**: Automatic dark/light mode, use ScrollView unless recycling needed (then FlatList)
- **Icons**: Use `IconSymbol` component for SF Symbols (iOS-specific)
- **Error handling**: Consistent error messages with user alerts via toast

## Important Notes
- **Incomplete refactoring**: Codebase transitioning from "patient" to "cards" terminology
- **Always run** `npm run typecheck` after code changes
- **Key dependencies**: expo-router, @tanstack/react-query, expo-audio, @gorhom/bottom-sheet
