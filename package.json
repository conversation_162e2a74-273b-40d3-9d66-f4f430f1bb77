{"name": "scribe-app", "main": "expo-router/entry", "version": "0.1.3", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@expo/react-native-action-sheet": "^4.1.1", "@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5.1.6", "@invertase/react-native-apple-authentication": "^2.4.1", "@react-native-google-signin/google-signin": "^15.0.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@ronradtke/react-native-markdown-display": "^8.1.0", "@shopify/flash-list": "1.7.6", "@siteed/expo-audio-studio": "^2.14.3", "@tanstack/react-query": "^5.80.6", "dayjs": "^1.11.13", "expo": "53.0.20", "expo-apple-authentication": "~7.2.4", "expo-audio": "~0.4.8", "expo-av": "~15.1.7", "expo-blur": "~14.1.5", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.6", "expo-dev-client": "~5.2.4", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-linking": "~7.1.7", "expo-localization": "~16.1.6", "expo-router": "~5.1.4", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "i18n-js": "^4.5.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-simple-toast": "^3.3.2", "react-native-svg": "15.11.2", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/uuid": "^10.0.0", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}