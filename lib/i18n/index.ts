import { getLocales } from "expo-localization";
import * as SecureStore from "expo-secure-store";
import { I18n } from "i18n-js";

import en from "./translations/en.json";
import zh from "./translations/zh.json";

// Key for storing language preference
const LANGUAGE_KEY = "user_language_preference";

// Initialize i18n with translations
const i18n = new I18n({
  en,
  zh,
});

// Enable fallback to English if translation is missing
i18n.enableFallback = true;
i18n.defaultLocale = "en";

// Function to get stored language preference
export async function getStoredLanguage(): Promise<string | null> {
  try {
    return await SecureStore.getItemAsync(LANGUAGE_KEY);
  } catch (error) {
    console.error("Error getting stored language:", error);
    return null;
  }
}

// Function to store language preference
export async function setStoredLanguage(language: string): Promise<void> {
  try {
    await SecureStore.setItemAsync(LANGUAGE_KEY, language);
  } catch (error) {
    console.error("Error storing language:", error);
  }
}

// Initialize language from stored preference or device locale
export async function initializeI18n(): Promise<void> {
  const storedLanguage = await getStoredLanguage();

  if (storedLanguage && storedLanguage !== "system") {
    // Use explicitly set language preference
    i18n.locale = storedLanguage;
    console.log("Using stored language preference:", storedLanguage);
  } else {
    // Use system language (either no preference or "system" is stored)
    const locales = getLocales();
    const deviceLocale = locales[0];

    console.log("Device locale info:", {
      languageCode: deviceLocale?.languageCode,
      languageTag: deviceLocale?.languageTag,
      regionCode: deviceLocale?.regionCode,
    });

    // Check if the device language is Chinese (including variants like zh-CN, zh-TW, zh-HK)
    const isChineseLocale =
      deviceLocale?.languageCode?.toLowerCase().startsWith("zh") ||
      deviceLocale?.languageTag?.toLowerCase().startsWith("zh");

    // Set language based on device locale
    i18n.locale = isChineseLocale ? "zh" : "en";

    console.log(
      "Auto-detected language:",
      i18n.locale,
      "based on device locale"
    );

    // Don't store the language if we're in system mode
    // If storedLanguage is null, it means user hasn't set a preference yet
    // If storedLanguage is "system", it means user explicitly wants system language
  }
}

// Function to change language
export async function changeLanguage(
  language: "en" | "zh" | "system"
): Promise<void> {
  if (language === "system") {
    // Store "system" as the preference
    await setStoredLanguage("system");

    // Re-detect system language
    const locales = getLocales();
    const deviceLocale = locales[0];
    const isChineseLocale =
      deviceLocale?.languageCode?.toLowerCase().startsWith("zh") ||
      deviceLocale?.languageTag?.toLowerCase().startsWith("zh");

    i18n.locale = isChineseLocale ? "zh" : "en";
  } else {
    i18n.locale = language;
    await setStoredLanguage(language);
  }
}

// Function to clear language preference (useful for testing or resetting)
export async function clearLanguagePreference(): Promise<void> {
  try {
    await SecureStore.deleteItemAsync(LANGUAGE_KEY);
  } catch (error) {
    console.error("Error clearing language preference:", error);
  }
}

// Function to get device language info (useful for debugging)
export function getDeviceLanguageInfo() {
  const locales = getLocales();
  const deviceLocale = locales[0];
  return {
    languageCode: deviceLocale?.languageCode,
    languageTag: deviceLocale?.languageTag,
    regionCode: deviceLocale?.regionCode,
  };
}

// Export the configured i18n instance
export default i18n;

// Function to check if using system language
export async function isUsingSystemLanguage(): Promise<boolean> {
  const storedLanguage = await getStoredLanguage();
  return storedLanguage === "system";
}

// Export commonly used functions
export const t = i18n.t.bind(i18n);
export const currentLocale = () => i18n.locale;
