import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import localizedFormat from "dayjs/plugin/localizedFormat";
import relativeTime from "dayjs/plugin/relativeTime";

// Configure dayjs plugins
dayjs.extend(localizedFormat);
dayjs.extend(relativeTime);

export interface DateTimeFormatOptions {
  includeTime?: boolean;
  includeYear?: boolean;
  format?: "short" | "long";
}

/**
 * Format a date string to a localized date/time string
 * @param dateString - The date string to format
 * @param locale - The locale to use for formatting (e.g., "en" or "zh")
 * @param options - Additional formatting options
 * @returns Formatted date string
 */
export function formatDateTime(
  dateString: string,
  locale: string,
  options: DateTimeFormatOptions = {}
): string {
  const { includeTime = true, includeYear = true, format = "short" } = options;

  // Set dayjs locale
  const dayjsLocale = locale === "zh" ? "zh-cn" : "en";
  
  // Create dayjs instance with locale
  const date = dayjs(dateString).locale(dayjsLocale);
  
  // Build format string based on options
  let formatString = "";
  
  if (format === "long") {
    // Long format
    if (includeYear) {
      formatString += "MMMM D, YYYY";
    } else {
      formatString += "MMMM D";
    }
    
    if (includeTime) {
      formatString += " [at] h:mm A";
    }
  } else {
    // Short format
    if (includeYear) {
      formatString += "MMM D, YYYY";
    } else {
      formatString += "MMM D";
    }
    
    if (includeTime) {
      formatString += " h:mm A";
    }
  }
  
  // For Chinese locale, use different format
  if (dayjsLocale === "zh-cn") {
    formatString = "";
    if (includeYear) {
      formatString += "YYYY年M月D日";
    } else {
      formatString += "M月D日";
    }
    
    if (includeTime) {
      formatString += " HH:mm";
    }
  }
  
  return date.format(formatString);
}

/**
 * Format a date string to show only the date
 * @param dateString - The date string to format
 * @param locale - The locale to use for formatting
 * @param options - Additional formatting options
 * @returns Formatted date string
 */
export function formatDate(
  dateString: string,
  locale: string,
  options: Omit<DateTimeFormatOptions, "includeTime"> = {}
): string {
  return formatDateTime(dateString, locale, { ...options, includeTime: false });
}

/**
 * Format a date string to show only the time
 * @param dateString - The date string to format
 * @param locale - The locale to use for formatting
 * @returns Formatted time string
 */
export function formatTime(dateString: string, locale: string): string {
  const dayjsLocale = locale === "zh" ? "zh-cn" : "en";
  const date = dayjs(dateString).locale(dayjsLocale);
  
  // Use 24-hour format for Chinese, 12-hour for English
  const formatString = dayjsLocale === "zh-cn" ? "HH:mm" : "h:mm A";
  
  return date.format(formatString);
}

/**
 * Get relative time string (e.g., "2 hours ago", "yesterday")
 * @param dateString - The date string to format
 * @param locale - The locale to use for formatting
 * @returns Relative time string
 */
export function getRelativeTime(dateString: string, locale: string): string {
  const dayjsLocale = locale === "zh" ? "zh-cn" : "en";
  
  // Configure relative time locale strings for Chinese
  if (dayjsLocale === "zh-cn") {
    dayjs.locale("zh-cn", {
      relativeTime: {
        future: "%s后",
        past: "%s前",
        s: "几秒",
        m: "1分钟",
        mm: "%d分钟",
        h: "1小时",
        hh: "%d小时",
        d: "1天",
        dd: "%d天",
        M: "1个月",
        MM: "%d个月",
        y: "1年",
        yy: "%d年",
      },
    });
  }
  
  const date = dayjs(dateString).locale(dayjsLocale);
  return date.fromNow();
}