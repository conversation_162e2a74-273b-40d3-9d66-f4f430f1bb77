import { router, Stack } from "expo-router";
import React, { useEffect } from "react";
import { ActivityIndicator, StyleSheet, Text, View } from "react-native";

import { Colors } from "@/constants/Colors";
import { useAuth } from "@/context/AuthContext";
import { useTranslation } from "@/context/LanguageContext";
import { useColorScheme } from "@/hooks/useColorScheme";
import { useProfile } from "@/lib/api/hooks";

export default function AppLayout() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const { session } = useAuth();
  const { t } = useTranslation();

  const { isLoading: isProfileLoading } = useProfile();

  useEffect(() => {
    console.log("session in (app)_layout", session);
    if (!session) {
      router.replace("/");
    }
  }, [session]);

  if (!session) {
    return null;
  }

  if (isProfileLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.tint} />
        <Text style={[styles.loadingText, { color: colors.text }]}>
          {t("common.loadingProfile")}
        </Text>
      </View>
    );
  }

  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.background,
        },
        headerTintColor: colors.text,
        headerShadowVisible: true,
        headerTitleAlign: "center",
        title: "",
      }}
    >
      <Stack.Screen name="home" />
      <Stack.Screen name="profile" />
      <Stack.Screen
        name="cards/[id]"
        options={{
          headerShown: false, // Hide header since cards/[id]/_layout.tsx has its own Stack
        }}
      />
    </Stack>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
});
