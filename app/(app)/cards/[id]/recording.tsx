import { MaterialIcons } from "@expo/vector-icons";
import {
  AudioRecording,
  ExpoAudioStreamModule,
  RecordingConfig,
  useAudioRecorder,
} from "@siteed/expo-audio-studio";
import { useAudioPlayer } from "expo-audio";
import * as FileSystem from "expo-file-system";
import { Stack, useLocalSearchParams, useRouter } from "expo-router";
import React, {
  useCallback,
  useEffect,
  useReducer,
  useRef,
  useState,
} from "react";
import {
  Alert,
  Animated,
  BackHandler,
  Platform,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";

import { ThemedText } from "@/components/ThemedText";
import { Button } from "@/components/ui/Button";
import { Colors } from "@/constants/Colors";
import { useTranslation } from "@/context/LanguageContext";
import { useColorScheme } from "@/hooks/useColorScheme";
import { useAnalyzeFileWithObjectName, useFileUploader } from "@/lib/api/hooks";

type RecordingState = {
  status: "idle" | "recording" | "paused" | "stopped" | "playing" | "error";
  hasPermission: boolean;
  duration: number; // The current duration of the active recording
  recordingUri: string | null;
  playbackPosition: number; // in seconds
  playbackDuration: number; // in seconds
  savedDuration: number; // The final duration of a completed recording
  error: string | null;
  transcription: string; // Real-time transcription text
};

type RecordingAction =
  | { type: "SET_PERMISSION"; payload: boolean }
  | { type: "START_RECORDING" }
  | { type: "PAUSE_RECORDING" }
  | { type: "RESUME_RECORDING" }
  | {
      type: "STOP_RECORDING";
      payload: {
        uri: string | null;
        duration: number;
        audioRecording: AudioRecording | null;
      };
    }
  | { type: "START_PLAYBACK" }
  | { type: "STOP_PLAYBACK" }
  | { type: "UPDATE_DURATION"; payload: number }
  | {
      type: "UPDATE_PLAYBACK_STATUS";
      payload: { position: number; duration: number; isPlaying: boolean };
    }
  | { type: "DISCARD" }
  | { type: "SET_ERROR"; payload: { message: string } }
  | { type: "UPDATE_TRANSCRIPTION"; payload: string }
  | { type: "APPEND_TRANSCRIPTION"; payload: string };

const initialState: RecordingState = {
  status: "idle",
  hasPermission: false,
  duration: 0,
  recordingUri: null,
  playbackPosition: 0,
  playbackDuration: 0,
  savedDuration: 0,
  error: null,
  transcription: "",
};

function recordingReducer(
  state: RecordingState,
  action: RecordingAction
): RecordingState {
  switch (action.type) {
    case "SET_PERMISSION":
      return { ...state, hasPermission: action.payload };
    case "START_RECORDING":
      return {
        ...initialState, // Reset everything
        hasPermission: state.hasPermission, // except permission
        status: "recording",
      };
    case "PAUSE_RECORDING":
      return { ...state, status: "paused" };
    case "RESUME_RECORDING":
      return { ...state, status: "recording" };
    case "STOP_RECORDING":
      return {
        ...state,
        status: "stopped",
        recordingUri: action.payload.uri,
        savedDuration: action.payload.duration,
        duration: 0, // Reset live duration
      };
    case "START_PLAYBACK":
      return { ...state, status: "playing", playbackPosition: 0 };
    case "STOP_PLAYBACK":
      return { ...state, status: "stopped", playbackPosition: 0 };
    case "UPDATE_DURATION":
      return { ...state, duration: action.payload };
    case "UPDATE_PLAYBACK_STATUS": {
      const isPlaying = action.payload.isPlaying;
      const position = Math.floor(action.payload.position);
      const playbackDuration = Math.floor(action.payload.duration);
      const hasFinished = position > 0 && position >= playbackDuration;

      return {
        ...state,
        status: isPlaying ? "playing" : "stopped",
        playbackPosition: hasFinished ? 0 : position,
        playbackDuration,
      };
    }
    case "DISCARD":
      return {
        ...initialState,
        hasPermission: state.hasPermission,
      };
    case "SET_ERROR":
      return { ...state, status: "error", error: action.payload.message };
    case "UPDATE_TRANSCRIPTION":
      return { ...state, transcription: action.payload };
    case "APPEND_TRANSCRIPTION":
      return { ...state, transcription: state.transcription + action.payload };
    default:
      return state;
  }
}

const ProgressBar = React.memo(function ProgressBar({
  playbackPosition,
  playbackDuration,
  colors,
}: {
  playbackPosition: number;
  playbackDuration: number;
  colors: (typeof Colors)["light"];
}) {
  if (playbackDuration <= 0) return null;

  const progressPercentage = (playbackPosition / playbackDuration) * 100;

  return (
    <View style={styles.progressBarContainer}>
      <View
        style={[styles.progressBar, { backgroundColor: `${colors.text}20` }]}
      >
        <View
          style={[
            styles.progressBarFill,
            {
              width: `${progressPercentage}%`,
              backgroundColor: colors.tint,
            },
          ]}
        />
      </View>
    </View>
  );
});

const RecordingControls = React.memo(function RecordingControls({
  isPaused,
  onPauseResume,
  onStop,
  t,
  colorScheme,
  isStopping,
}: {
  isPaused: boolean;
  onPauseResume: () => void;
  onStop: () => void;
  t: (key: any) => string;
  colorScheme: "light" | "dark";
  isStopping: boolean;
}) {
  return (
    <>
      <Button
        title={isPaused ? t("card.resumeRecording") : t("card.pauseRecording")}
        variant="secondary"
        onPress={onPauseResume}
        icon={
          <MaterialIcons
            name={isPaused ? "play-arrow" : "pause"}
            size={20}
            color={colorScheme === "dark" ? "#e5e7eb" : "#374151"}
          />
        }
        containerStyle={styles.controlButton}
      />
      {isPaused && (
        <Button
          title={t("card.stopRecording")}
          onPress={onStop}
          icon={<MaterialIcons name="stop" size={20} color="white" />}
          containerStyle={styles.stopButton}
          loading={isStopping}
          disabled={isStopping}
        />
      )}
    </>
  );
});

const PlaybackControls = React.memo(function PlaybackControls({
  isPlaying,
  isSaving,
  onPlay,
  onStopPlayback,
  onSave,
  t,
  colorScheme,
}: {
  isPlaying: boolean;
  isSaving: boolean;
  onPlay: () => void;
  onStopPlayback: () => void;
  onSave: () => void;
  t: (key: any) => string;
  colorScheme: "light" | "dark";
}) {
  return (
    <>
      {isPlaying ? (
        <Button
          title={t("card.stopPlayback")}
          variant="secondary"
          onPress={onStopPlayback}
          icon={
            <MaterialIcons
              name="stop"
              size={20}
              color={colorScheme === "dark" ? "#e5e7eb" : "#374151"}
            />
          }
          containerStyle={styles.controlButton}
        />
      ) : (
        <Button
          title={t("card.playRecording")}
          variant="secondary"
          onPress={onPlay}
          icon={
            <MaterialIcons
              name="play-arrow"
              size={20}
              color={colorScheme === "dark" ? "#e5e7eb" : "#374151"}
            />
          }
          containerStyle={styles.controlButton}
        />
      )}
      <Button
        title={t("card.saveRecording")}
        onPress={onSave}
        icon={<MaterialIcons name="save" size={20} color="white" />}
        containerStyle={styles.saveButton}
        loading={isSaving}
      />
    </>
  );
});

export default function RecordingScreen() {
  const router = useRouter();
  const { id: cardId } = useLocalSearchParams();
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const { t } = useTranslation();

  // State management
  const [state, dispatch] = useReducer(recordingReducer, initialState);
  const [isSaving, setIsSaving] = useState(false);
  const [isStopping, setIsStopping] = useState(false);

  // Refs
  const animations = useRef<Animated.CompositeAnimation[]>([]);
  const animationTimeouts = useRef<ReturnType<typeof setTimeout>[]>([]);
  const audioChunksRef = useRef<string[]>([]);
  const transcriptionScrollRef = useRef<ScrollView>(null);

  // Audio hooks
  const {
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    durationMs,
    size,
    isRecording,
    isPaused,
  } = useAudioRecorder();
  const audioPlayer = useAudioPlayer();

  // Animation values
  const waveformBars = useRef(
    Array.from({ length: 20 }, () => new Animated.Value(0.3))
  ).current;

  // API mutations for upload flow
  const fileUploaderMutation = useFileUploader(cardId as string);
  const analyzeFileMutation = useAnalyzeFileWithObjectName(cardId as string);

  // Function to send audio chunks to transcription API
  const sendAudioChunksToTranscription = useCallback(
    async (chunks?: string[]) => {
      const chunksToSend = chunks || audioChunksRef.current;
      if (chunksToSend.length === 0) return;

      const chunkCount = chunksToSend.length;

      try {
        const ttsApiDomain = process.env.EXPO_PUBLIC_TTS_API_DOMAIN || "";
        const response = await fetch(`${ttsApiDomain}/api/simple-asr`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            audioChunks: chunksToSend,
          }),
        });

        if (response.ok) {
          const result = await response.json();
          if (result.transcription) {
            dispatch({
              type: "APPEND_TRANSCRIPTION",
              payload: result.transcription,
            });
          }
        } else {
          console.error(
            "Transcription API error:",
            response.status,
            response.statusText
          );
        }
      } catch (error) {
        console.error("Error sending audio to transcription:", error);
      }
    },
    []
  );

  // ===== Effects =====

  // Request permissions on mount
  useEffect(() => {
    const getPermissions = async () => {
      try {
        const { status } =
          await ExpoAudioStreamModule.requestPermissionsAsync();
        dispatch({ type: "SET_PERMISSION", payload: status === "granted" });
        if (status !== "granted") {
          Alert.alert(
            t("card.permissionRequired"),
            t("card.grantMicPermission")
          );
        }
      } catch {
        // Try to proceed anyway - permissions might be already granted
        dispatch({ type: "SET_PERMISSION", payload: true });
      }
    };
    getPermissions();
  }, [t]);

  // Update duration from the recorder hook
  useEffect(() => {
    if (isRecording || isPaused) {
      const durationInSeconds = Math.floor(durationMs / 1000);
      dispatch({ type: "UPDATE_DURATION", payload: durationInSeconds });
    }
  }, [durationMs, isRecording, isPaused]);

  // Auto-scroll transcription when new text is added
  useEffect(() => {
    if (transcriptionScrollRef.current && state.transcription) {
      transcriptionScrollRef.current.scrollToEnd({ animated: true });
    }
  }, [state.transcription]);

  // Function to stop animations
  const stopAnimations = useCallback(
    (resetBars = true) => {
      // Clear all timeouts first
      animationTimeouts.current.forEach((timeout) => {
        clearTimeout(timeout);
      });
      animationTimeouts.current = [];

      // Stop all animations
      animations.current.forEach((anim) => {
        if (anim) {
          anim.stop();
        }
      });
      animations.current = [];

      // Only reset bars when fully stopping (not pausing)
      if (resetBars) {
        waveformBars.forEach((animValue) => {
          animValue.setValue(0.3);
        });
      }
    },
    [waveformBars]
  );

  // Function to start animations
  const startAnimations = useCallback(() => {
    // Clear any existing animations first (but don't reset bars)
    stopAnimations(false);

    waveformBars.forEach((animValue, index) => {
      const createAnimation = () => {
        return Animated.loop(
          Animated.sequence([
            Animated.timing(animValue, {
              toValue: Math.random() * 0.8 + 0.2,
              duration: 300 + Math.random() * 400,
              useNativeDriver: true,
            }),
            Animated.timing(animValue, {
              toValue: 0.2,
              duration: 300 + Math.random() * 400,
              useNativeDriver: true,
            }),
          ])
        );
      };

      // Start with a delay for each bar
      const timeout = setTimeout(() => {
        const anim = createAnimation();
        animations.current[index] = anim;
        anim.start();
      }, index * 50);

      // Store timeout reference
      animationTimeouts.current[index] = timeout;
    });
  }, [waveformBars, stopAnimations]);

  useEffect(() => {
    // Start or stop animations based on recording and playback state
    if (
      (state.status === "recording" || state.status === "playing") &&
      animations
    ) {
      startAnimations();
    } else if (state.status !== "paused") {
      // Only reset bars when recording is completely stopped and not playing
      stopAnimations(true);
    } else {
      // Just pause animations, don't reset bars
      stopAnimations(false);
    }

    // Cleanup on unmount
    return () => {
      stopAnimations(true);
    };
  }, [state.status, startAnimations, stopAnimations]);

  // ===== Helper Functions =====

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  // Helper functions to simplify conditional logic
  const getTimerDisplay = () => {
    if (state.status === "recording" || state.status === "paused") {
      return formatTime(state.duration);
    }
    if (state.status === "playing") {
      return `${formatTime(state.playbackPosition)} / ${formatTime(
        state.playbackDuration
      )}`;
    }
    if (state.recordingUri) {
      return formatTime(state.savedDuration);
    }
    return formatTime(0);
  };

  const getStatusText = () => {
    switch (state.status) {
      case "recording":
        return t("card.recordingInProgress");
      case "paused":
        return t("card.recordingPaused");
      case "stopped":
        return t("card.recordingComplete");
      case "playing":
        return t("card.playingRecording");
      default:
        return t("card.readyToRecord");
    }
  };

  const handleStart = useCallback(async () => {
    try {
      if (!state.hasPermission) {
        Alert.alert(t("card.permissionRequired"), t("card.grantMicPermission"));
        return;
      }

      // Reset audio chunks when starting new recording
      audioChunksRef.current = [];
      dispatch({ type: "UPDATE_TRANSCRIPTION", payload: "" });

      // Configure recording options
      const config: RecordingConfig = {
        interval: 50, // Emit recording data every 50ms (20 chunks per second)
        enableProcessing: false, // Disable audio analysis for now
        sampleRate: 16000, // Match the original sample rate
        channels: 1, // Mono recording
        encoding: "pcm_16bit", // PCM encoding

        // Configure audio output files
        output: {
          // Primary WAV file
          primary: {
            enabled: true,
          },
          // Compressed file
          compressed: {
            enabled: true,
            format: "aac",
            bitrate: 48000, // Match original bitrate
          },
        },

        // Auto-resume after interruption
        autoResumeAfterInterruption: false,

        // Handle audio stream data for real-time transcription
        onAudioStream: async (audioData) => {
          if (!audioData.data || audioData.data.length === 0) return;

          try {
            // Check if data is already base64 encoded or needs conversion
            let base64Audio: string;

            if (typeof audioData.data === "string") {
              // Data is already base64 encoded
              base64Audio = audioData.data;
            } else {
              // Convert Float32Array to base64
              const pcmData = new Int16Array(audioData.data.length);
              for (let i = 0; i < audioData.data.length; i++) {
                const s = Math.max(-1, Math.min(1, audioData.data[i]));
                pcmData[i] = s < 0 ? s * 0x8000 : s * 0x7fff;
              }

              // Convert to base64
              const buffer = new ArrayBuffer(pcmData.length * 2);
              const view = new DataView(buffer);
              for (let i = 0; i < pcmData.length; i++) {
                view.setInt16(i * 2, pcmData[i], true); // little endian
              }

              // Convert ArrayBuffer to base64
              const bytes = new Uint8Array(buffer);
              let binary = "";
              for (let i = 0; i < bytes.byteLength; i++) {
                binary += String.fromCharCode(bytes[i]);
              }
              base64Audio = btoa(binary);
            }

            // Collect audio chunks
            audioChunksRef.current.push(base64Audio);

            // Send chunks every 10 intervals (500ms worth of audio)
            if (audioChunksRef.current.length === 10) {
              // Copy chunks and clear immediately to avoid race condition
              const chunksToSend = [...audioChunksRef.current];
              audioChunksRef.current = [];

              // Send the copied chunks
              await sendAudioChunksToTranscription(chunksToSend);
            }
          } catch (error) {
            console.error("Error processing audio stream:", error);
          }
        },
      };

      await startRecording(config);
      dispatch({ type: "START_RECORDING" });
    } catch {
      Alert.alert("Error", "Failed to start recording");
    }
  }, [startRecording, state.hasPermission, t, sendAudioChunksToTranscription]);

  const handlePauseResume = useCallback(async () => {
    if (state.status !== "recording" && state.status !== "paused") return;

    try {
      if (state.status === "paused") {
        await resumeRecording();
        dispatch({ type: "RESUME_RECORDING" });
      } else {
        await pauseRecording();
        dispatch({ type: "PAUSE_RECORDING" });
        // Stop animations but keep bars in current position
        stopAnimations(false);
      }
    } catch {
      Alert.alert("Error", "Failed to pause/resume recording");
    }
  }, [pauseRecording, resumeRecording, state.status, stopAnimations]);

  const handleStop = useCallback(async () => {
    try {
      setIsStopping(true);

      // Send any remaining audio chunks before stopping
      if (audioChunksRef.current.length > 0) {
        await sendAudioChunksToTranscription();
        audioChunksRef.current = [];
      }

      const result = await stopRecording();

      if (!result) {
        throw new Error("No recording result available");
      }

      // Use the compressed file URI from the result
      const fileToUse = result.compression?.compressedFileUri;

      if (!fileToUse) {
        throw new Error("No recording URI available");
      }

      // Rename the file immediately after stopping
      // The library should have saved it with the compressed format based on our config
      const fileExtension = "m4a";
      const timestamp = new Date()
        .toISOString()
        .replace(/[-:T.]/g, "")
        .slice(0, -5);
      const fileName = `${cardId}-${timestamp}.${fileExtension}`;
      const renamedPath = `${FileSystem.documentDirectory}${fileName}`;

      await FileSystem.moveAsync({
        from: fileToUse,
        to: renamedPath,
      });

      // Get file size for debugging
      const fileInfo = await FileSystem.getInfoAsync(renamedPath);
      if (fileInfo.exists && "size" in fileInfo) {
        const fileSizeInMB = (fileInfo.size / (1024 * 1024)).toFixed(2);
        console.log(
          `Recording saved: ${fileName}, Size: ${fileSizeInMB} MB (${fileInfo.size} bytes)`
        );
      }

      const finalSec = Math.floor(result.durationMs / 1000);

      dispatch({
        type: "STOP_RECORDING",
        payload: {
          uri: renamedPath,
          duration: finalSec,
          audioRecording: result,
        },
      });

      // Reset waveform when stopping
      stopAnimations(true);
    } catch {
      Alert.alert("Error", "Failed to stop recording");
    } finally {
      setIsStopping(false);
    }
  }, [stopRecording, cardId, stopAnimations, sendAudioChunksToTranscription]);

  const handlePlay = useCallback(async () => {
    if (!state.recordingUri) return;

    try {
      if (state.status === "playing") {
        await audioPlayer.pause();
        dispatch({ type: "STOP_PLAYBACK" }); // This will effectively pause it
        // Pause animations but don't reset
        stopAnimations(false);
      } else {
        await audioPlayer.replace({ uri: state.recordingUri });
        await audioPlayer.play();
        dispatch({ type: "START_PLAYBACK" });
      }
    } catch {
      Alert.alert("Error", "Failed to play recording");
    }
  }, [audioPlayer, state.recordingUri, state.status, stopAnimations]);

  const handleStopPlayback = useCallback(async () => {
    try {
      await audioPlayer.pause();
      await audioPlayer.seekTo(0);
      dispatch({ type: "STOP_PLAYBACK" });
      // Stop and reset animations when stopping playback
      stopAnimations(true);
    } catch {}
  }, [audioPlayer, stopAnimations]);

  const handleSaveRecording = useCallback(async () => {
    const uriToSave = state.recordingUri;
    if (!uriToSave) return;

    try {
      Alert.alert(
        t("card.saveRecordingConfirmTitle"),
        t("card.saveRecordingConfirmMessage"),
        [
          { text: t("common.cancel"), style: "cancel" },
          {
            text: t("common.save"),
            onPress: async () => {
              setIsSaving(true);
              try {
                // Step 1: Get file info from the already renamed file
                const fileExtension = "m4a";
                const contentType = "audio/m4a";

                // Extract filename from the uri (it's already renamed)
                const fileName =
                  uriToSave.split("/").pop() || `${cardId}.${fileExtension}`;

                // Step 2: Get presigned URL
                const uploaderResponse = await fileUploaderMutation.mutateAsync(
                  {
                    fileName,
                    contentType,
                  }
                );

                // Step 3: Upload file to presigned URL
                // Read the file as a blob for binary upload
                const uploadResponse = await fetch(uploaderResponse.url, {
                  method: "PUT",
                  headers: {
                    "Content-Type": contentType,
                    "x-oss-meta-original-filename": fileName,
                  },
                  body: await fetch(uriToSave).then((res) => res.blob()),
                });

                if (!uploadResponse.ok) {
                  throw new Error("Failed to upload file to OSS");
                }

                console.log("uploadResponse", uploadResponse);

                // Step 4: Notify backend that upload is complete
                await analyzeFileMutation.mutateAsync({
                  analysisType: "clinical_note",
                  objectName: uploaderResponse.objectName,
                });

                // Delete the audio file after successful upload
                try {
                  await FileSystem.deleteAsync(uriToSave, {
                    idempotent: true,
                  });
                } catch (deleteError) {
                  console.error("Failed to delete audio file:", deleteError);
                }

                router.back();
              } catch (error) {
                Alert.alert(
                  t("common.error"),
                  error instanceof Error ? error.message : t("common.retry")
                );
              } finally {
                setIsSaving(false);
              }
            },
          },
        ]
      );
    } catch {
      Alert.alert("Error", "Failed to save recording");
    }
  }, [
    state.recordingUri,
    router,
    cardId,
    fileUploaderMutation,
    analyzeFileMutation,
    t,
    setIsSaving,
  ]);

  // Monitor playback status
  useEffect(() => {
    const subscription = audioPlayer.addListener(
      "playbackStatusUpdate",
      (status) => {
        if (status.isLoaded && "playing" in status) {
          // Update playback position and duration
          if ("currentTime" in status && "duration" in status) {
            const hasFinished =
              status.currentTime >= status.duration && status.duration > 0;

            dispatch({
              type: "UPDATE_PLAYBACK_STATUS",
              payload: {
                position: status.currentTime,
                duration: status.duration,
                isPlaying: status.playing && !hasFinished,
              },
            });

            // Check if playback has finished to stop animations
            if (hasFinished) {
              // Reset waveform when playback ends
              stopAnimations(true);
            }
          }
        }
      }
    );

    return () => {
      subscription?.remove();
    };
  }, [audioPlayer, stopAnimations]);

  // Custom back handler
  const handleBackPress = useCallback(() => {
    if (state.status === "idle") {
      // No recording in progress, allow navigation
      router.back();
      return;
    }

    // Show confirmation dialog
    Alert.alert(
      t("card.leaveRecordingTitle"),
      state.status === "recording" || state.status === "paused"
        ? t("card.leaveRecordingActiveMessage")
        : t("card.leaveRecordingCompleteMessage"),
      [
        { text: t("common.cancel"), style: "cancel" },
        {
          text: t("common.leave"),
          style: "destructive",
          onPress: async () => {
            // Stop recording if active
            if (state.status === "recording" || state.status === "paused") {
              try {
                await stopRecording();
              } catch {
                // Ignore errors when stopping
              }
            }
            // Stop playback if playing
            if (state.status === "playing") {
              try {
                await audioPlayer.pause();
              } catch {
                // Ignore errors when pausing
              }
            }
            // Delete unsaved recording if exists
            if (state.recordingUri) {
              try {
                await FileSystem.deleteAsync(state.recordingUri, {
                  idempotent: true,
                });
              } catch (deleteError) {
                console.error(
                  "Failed to delete unsaved recording:",
                  deleteError
                );
              }
            }
            // Navigate back
            router.back();
          },
        },
      ]
    );
  }, [state.status, state.recordingUri, stopRecording, audioPlayer, router, t]);

  // Handle Android hardware back button
  useEffect(() => {
    if (Platform.OS !== "android") return;

    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      () => {
        // If there's no recording, allow normal back navigation
        if (state.status === "idle") {
          return false;
        }

        // Show the same confirmation dialog
        handleBackPress();
        return true; // Prevent default back action
      }
    );

    return () => backHandler.remove();
  }, [state.status, handleBackPress]);

  if (!state.hasPermission) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.content}>
          <ThemedText style={styles.permissionText}>
            {t("card.grantMicPermission")}
          </ThemedText>
        </View>
      </View>
    );
  }

  return (
    <>
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Stack.Screen
          options={{
            headerTitle: t("card.recording"),
            headerLeft: () => (
              <TouchableOpacity
                onPress={handleBackPress}
                style={{ padding: 8 }}
              >
                <MaterialIcons
                  name={Platform.OS === "ios" ? "arrow-back-ios" : "arrow-back"}
                  size={24}
                  color={colors.text}
                />
              </TouchableOpacity>
            ),
          }}
        />
        <View style={styles.content}>
          <View style={styles.visualizationContainer}>
            {/* Waveform visualization */}
            <View style={styles.waveform}>
              {waveformBars.map((animValue, index) => (
                <Animated.View
                  key={index}
                  style={[
                    styles.waveformBar,
                    {
                      backgroundColor:
                        state.status === "playing" ? "#3b82f6" : "#ef4444",
                      opacity:
                        state.status === "recording" ||
                        state.status === "playing"
                          ? 1
                          : 0.5,
                      transform: [
                        {
                          scaleY: animValue,
                        },
                      ],
                    },
                  ]}
                />
              ))}
            </View>
          </View>

          <ThemedText style={styles.timer}>{getTimerDisplay()}</ThemedText>

          {state.status === "playing" && (
            <ProgressBar
              playbackPosition={state.playbackPosition}
              playbackDuration={state.playbackDuration}
              colors={colors}
            />
          )}

          <ThemedText style={styles.status}>{getStatusText()}</ThemedText>

          {/* Real-time transcription display */}
          {state.transcription && (
            <View style={styles.transcriptionContainer}>
              <ThemedText style={styles.transcriptionLabel}>
                {t("card.transcription") || "Transcription:"}
              </ThemedText>
              <ScrollView
                ref={transcriptionScrollRef}
                style={{ maxHeight: 160 }}
                showsVerticalScrollIndicator={true}
              >
                <ThemedText style={styles.transcriptionText}>
                  {state.transcription}
                </ThemedText>
              </ScrollView>
            </View>
          )}

          <View style={styles.controls}>
            {state.status === "idle" && (
              <Button
                title={t("card.startRecording")}
                onPress={handleStart}
                icon={<MaterialIcons name="mic" size={20} color="white" />}
                containerStyle={styles.startButton}
              />
            )}

            {(state.status === "recording" || state.status === "paused") && (
              <RecordingControls
                isPaused={state.status === "paused"}
                onPauseResume={handlePauseResume}
                onStop={handleStop}
                t={t}
                colorScheme={colorScheme ?? "light"}
                isStopping={isStopping}
              />
            )}

            {(state.status === "stopped" || state.status === "playing") &&
              state.recordingUri && (
                <PlaybackControls
                  isPlaying={state.status === "playing"}
                  isSaving={isSaving}
                  onPlay={handlePlay}
                  onStopPlayback={handleStopPlayback}
                  onSave={handleSaveRecording}
                  t={t}
                  colorScheme={colorScheme ?? "light"}
                />
              )}
          </View>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
  },
  visualizationContainer: {
    height: 100,
    marginBottom: 40,
    justifyContent: "center",
  },
  waveform: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    height: 60,
    gap: 4,
  },
  waveformBar: {
    width: 3,
    height: 60,
    borderRadius: 1.5,
  },
  timer: {
    fontSize: 32,
    lineHeight: 32,
    fontWeight: "600",
    fontVariant: ["tabular-nums"],
    marginBottom: 8,
  },
  status: {
    fontSize: 16,
    opacity: 0.7,
    marginBottom: 20,
  },
  transcriptionContainer: {
    width: "100%",
    maxWidth: 350,
    marginBottom: 30,
    marginTop: 15,
    padding: 16,
    backgroundColor: "rgba(0,0,0,0.05)",
    borderRadius: 12,
    minHeight: 100,
  },
  transcriptionLabel: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 8,
    opacity: 0.7,
  },
  transcriptionText: {
    fontSize: 16,
    lineHeight: 24,
  },
  controls: {
    flexDirection: "row",
    gap: 16,
  },
  controlButton: {
    minWidth: 140,
  },
  startButton: {
    minWidth: 160,
    backgroundColor: "#3b82f6",
  },
  stopButton: {
    backgroundColor: "#dc2626",
  },
  saveButton: {
    minWidth: 140,
    backgroundColor: "#10b981",
  },
  permissionText: {
    fontSize: 16,
    textAlign: "center",
    opacity: 0.7,
  },
  backButtonContainer: {
    position: "absolute",
    bottom: 40,
  },
  progressBarContainer: {
    width: "80%",
    marginTop: 16,
    marginBottom: 8,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
    overflow: "hidden",
  },
  progressBarFill: {
    height: "100%",
    borderRadius: 2,
  },
});
