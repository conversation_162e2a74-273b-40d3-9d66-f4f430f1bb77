import { MaterialIcons } from "@expo/vector-icons";
import { Stack, useLocalSearchParams, useRouter } from "expo-router";
import React from "react";
import { TouchableOpacity } from "react-native";

import { Colors } from "@/constants/Colors";
import { useTranslation } from "@/context/LanguageContext";
import { useColorScheme } from "@/hooks/useColorScheme";
import { useCard } from "@/lib/api/hooks";

export default function CardLayout() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const { t } = useTranslation();

  // Fetch card data for the header
  const { data: cardData, isLoading: isCardLoading } = useCard(id as string);

  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.background,
        },
        headerTintColor: colors.text,
        headerTitleAlign: "center",
        headerShadowVisible: true,
      }}
    >
      <Stack.Screen
        name="(tabs)"
        options={{
          headerTitle: isCardLoading
            ? t("common.loading")
            : cardData?.card.name || "Patient",
          headerLeft: () => (
            <TouchableOpacity
              onPress={() => router.back()}
              style={{ paddingRight: 15 }}
            >
              <MaterialIcons name="arrow-back" size={24} color={colors.text} />
            </TouchableOpacity>
          ),
        }}
      />
      <Stack.Screen
        name="recording"
        options={{
          presentation: "modal",
          headerTitle: t("card.recording"),
          // The recording screen will set its own headerLeft
        }}
      />
      <Stack.Screen
        name="files/[fileId]"
        options={{
          headerTitle: t("card.soapNote"),
        }}
      />
    </Stack>
  );
}
