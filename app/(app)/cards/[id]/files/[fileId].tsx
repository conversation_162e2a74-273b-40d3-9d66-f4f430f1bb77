import { Ionicons } from "@expo/vector-icons";
import { BottomSheetModal } from "@gorhom/bottom-sheet";
import { useQueryClient } from "@tanstack/react-query";
import { Audio, AVPlaybackStatus } from "expo-av";
import * as Clipboard from "expo-clipboard";
import { Stack, useLocalSearchParams, useRouter } from "expo-router";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Toast from "react-native-simple-toast";

import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { FileOptionsBottomSheet } from "@/components/ui/FileOptionsBottomSheet";
import { Colors } from "@/constants/Colors";
import { useLanguage, useTranslation } from "@/context/LanguageContext";
import { useColorScheme } from "@/hooks/useColorScheme";
import { useCardFile, useRegenerateCardFile } from "@/lib/api/hooks";
import {
  type TranscriptionSoapContext,
  type TranscriptionSoapResult,
} from "@/lib/api/types";
import { formatDateTime } from "@/lib/utils/datetime";
import Markdown, { MarkdownIt } from "@ronradtke/react-native-markdown-display";

export default function CardFileDetailScreen() {
  const { id, fileId } = useLocalSearchParams<{ id: string; fileId: string }>();
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const { locale } = useLanguage();
  const { t } = useTranslation();
  const router = useRouter();
  const queryClient = useQueryClient();

  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [position, setPosition] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isBuffering, setIsBuffering] = useState(false);

  // Bottom sheet ref
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);

  const {
    data,
    isLoading: isDataLoading,
    error,
    refetch,
  } = useCardFile(fileId as string);

  const { mutate: regenerateFile } = useRegenerateCardFile(id);

  const cardFile = data?.cardFile;
  const API_URL = process.env.EXPO_PUBLIC_API_DOMAIN || "http://localhost:3000";

  // Check if file is pending or processing
  const isPendingOrProcessing = useMemo(() => {
    return cardFile?.status === "pending" || cardFile?.status === "processing";
  }, [cardFile?.status]);

  // Auto-refetch when file is pending or processing
  useEffect(() => {
    if (isPendingOrProcessing) {
      console.log("File is pending/processing, setting up auto-refetch");
      const interval = setInterval(async () => {
        // Invalidate the cache to ensure fresh data
        await queryClient.invalidateQueries({ queryKey: ["cardFile", fileId] });
        refetch();
      }, 10000); // Refetch every 10 seconds

      return () => clearInterval(interval);
    }
  }, [isPendingOrProcessing, refetch, queryClient, fileId]);

  // Setup Audio mode on mount
  useEffect(() => {
    const setupAudio = async () => {
      try {
        await Audio.setAudioModeAsync({
          allowsRecordingIOS: false,
          staysActiveInBackground: true,
          playsInSilentModeIOS: true,
          shouldDuckAndroid: true,
          playThroughEarpieceAndroid: false,
        });
      } catch (error) {
        console.error("Error setting audio mode:", error);
      }
    };
    setupAudio();
  }, []);

  // Playback status update callback
  const onPlaybackStatusUpdate = useCallback((status: AVPlaybackStatus) => {
    if (!status.isLoaded) {
      // Update status for unloaded state
      if (status.error) {
        console.error("Playback error:", status.error);
      }
    } else {
      // Update status for loaded state
      setIsPlaying(status.isPlaying);
      setPosition(status.positionMillis / 1000);
      setDuration(status.durationMillis ? status.durationMillis / 1000 : 0);
      setIsBuffering(status.isBuffering);

      // Handle playback completion
      if (status.didJustFinish && !status.isLooping) {
        console.log("Playback finished, resetting position");
        setIsPlaying(false);
        setPosition(0);
      }

      // Log status for debugging
      console.log("Playback status:", {
        isPlaying: status.isPlaying,
        isBuffering: status.isBuffering,
        didJustFinish: status.didJustFinish,
        position: status.positionMillis / 1000,
        duration: status.durationMillis ? status.durationMillis / 1000 : 0,
      });
    }
  }, []);

  // Load audio when data is available
  useEffect(() => {
    if (!cardFile) return;

    let isMounted = true;

    const loadAudio = async () => {
      try {
        setIsLoading(true);

        // Unload previous sound if exists
        if (sound) {
          await sound.unloadAsync();
          setSound(null);
        }

        let url: string | null = null;

        // Prefer signedUrl as it should work without headers
        if (cardFile.signedUrl) {
          url = cardFile.signedUrl;
          console.log("Using signedUrl for audio:", url);
        } else if (cardFile.filePath) {
          // Construct full URL from filePath
          if (
            cardFile.filePath.startsWith("http://") ||
            cardFile.filePath.startsWith("https://")
          ) {
            url = cardFile.filePath;
          } else {
            const cleanPath = cardFile.filePath.startsWith("/")
              ? cardFile.filePath.slice(1)
              : cardFile.filePath;
            url = `${API_URL}/${cleanPath}`;
          }
          console.log("Using constructed URL for audio:", url);
        }

        if (!url) {
          console.error("No valid audio URL found");
          setIsLoading(false);
          return;
        }

        // Create and load the sound
        console.log("Creating sound object...");
        const { sound: newSound } = await Audio.Sound.createAsync(
          { uri: url },
          {
            shouldPlay: false,
            progressUpdateIntervalMillis: 1000,
          },
          onPlaybackStatusUpdate
        );

        if (isMounted) {
          setSound(newSound);
          setIsLoading(false);
          console.log("Audio loaded successfully");

          // Get initial status
          const status = await newSound.getStatusAsync();
          if (status.isLoaded) {
            setDuration(
              status.durationMillis ? status.durationMillis / 1000 : 0
            );
          }
        }
      } catch (error) {
        console.error("Error loading audio:", error);
        if (isMounted) {
          setIsLoading(false);
          Alert.alert(
            "Audio Error",
            "Failed to load audio file. Please check your connection and try again."
          );
        }
      }
    };

    loadAudio();

    // Cleanup
    return () => {
      isMounted = false;
    };
  }, [cardFile, API_URL]);

  // Cleanup sound on unmount
  useEffect(() => {
    return () => {
      if (sound) {
        console.log("Unloading sound on unmount");
        sound.unloadAsync();
      }
    };
  }, [sound]);

  // Extract transcript and SOAP note from contexts
  const markdownContext = cardFile?.contexts?.find(
    (ctx): ctx is TranscriptionSoapContext =>
      ctx.contextType === "transcription_soap"
  );

  const sectionTitle = (key: string) => {
    switch (key) {
      case "clinical_note":
        return t("card.soapNote");
      case "transcription_soap":
        return t("card.transcript");
      default:
    }
  };

  // Parse the JSON string from context.result
  let markdownContent: string | undefined;
  if (markdownContext?.result) {
    try {
      const parsedResult: TranscriptionSoapResult = JSON.parse(
        markdownContext.result
      );
      markdownContent = `## ${sectionTitle("transcription_soap")}\n${
        parsedResult.transcriptionText
      }\n\n## ${sectionTitle("clinical_note")}\n${
        parsedResult.clinicalNoteText
      }`;
    } catch (error) {
      console.error("Error parsing context result:", error);
      // Fallback to treating it as a string if parsing fails
      markdownContent = markdownContext.result;
    }
  }

  const handlePlayPause = useCallback(async () => {
    if (!sound) {
      console.log("Sound not loaded");
      return;
    }

    try {
      const status = await sound.getStatusAsync();

      if (!status.isLoaded) {
        console.log("Sound is not loaded");
        return;
      }

      if (status.isPlaying) {
        console.log("Pausing audio");
        await sound.pauseAsync();
      } else {
        // Check if we're at the end of the audio
        if (
          status.durationMillis &&
          status.positionMillis >= status.durationMillis - 100
        ) {
          console.log("Audio at end, restarting from beginning");
          await sound.setPositionAsync(0);
        }
        console.log("Playing audio");
        await sound.playAsync();
      }
    } catch (error) {
      console.error("Error playing/pausing audio:", error);
      Alert.alert("Playback Error", "Failed to play audio. Please try again.");
    }
  }, [sound]);

  const formatTime = (seconds: number) => {
    if (isNaN(seconds) || seconds < 0) return "0:00";
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const handleMenuPress = useCallback(() => {
    bottomSheetModalRef.current?.present();
  }, []);

  const handleReprocess = useCallback(() => {
    Alert.alert(t("card.reprocessFileTitle"), t("card.reprocessFileMessage"), [
      {
        text: t("common.cancel"),
        style: "cancel",
      },
      {
        text: t("card.reprocess"),
        style: "destructive",
        onPress: () => {
          regenerateFile(fileId, {
            onSuccess: () => {
              Toast.show(t("card.fileReprocessStarted"), Toast.SHORT);
              refetch();
            },
            onError: () => {
              Toast.show(t("card.fileReprocessFailed"), Toast.SHORT);
            },
          });
        },
      },
    ]);
  }, [t, regenerateFile, fileId, refetch]);

  const handleReprocessSuccess = useCallback(() => {
    router.back();
  }, [router]);

  // Extract clinical note text for copying
  const getClinicalNoteText = useCallback(() => {
    if (!markdownContext?.result) return "";

    try {
      const parsedResult: TranscriptionSoapResult = JSON.parse(
        markdownContext.result
      );
      return parsedResult.clinicalNoteText || "";
    } catch (error) {
      console.error("Error parsing clinical note:", error);
      return "";
    }
  }, [markdownContext]);

  const handleCopyClinicalNote = useCallback(async () => {
    const clinicalNoteText = getClinicalNoteText();

    if (!clinicalNoteText) {
      Toast.show(t("card.noClinicalNoteFound"), Toast.SHORT);
      return;
    }

    try {
      await Clipboard.setStringAsync(clinicalNoteText);
      Toast.show(t("common.copied"), Toast.SHORT);
    } catch (error) {
      console.error("Error copying to clipboard:", error);
      Toast.show(t("common.error"), Toast.SHORT);
    }
  }, [getClinicalNoteText, t]);

  // Markdown styles for transcription + SOAP display
  const markdownStyles = useMemo(() => {
    return StyleSheet.create({
      body: {
        color: colors.text,
        fontSize: 16,
        lineHeight: 22,
      },
      heading1: {
        color: colors.text,
        fontSize: 24,
        fontWeight: "bold",
        marginVertical: 6,
      },
      heading2: {
        color: colors.text,
        fontSize: 20,
        fontWeight: "bold",
        marginTop: 25,
        marginBottom: 10,
      },
      heading3: {
        color: colors.text,
        fontSize: 18,
        fontWeight: "bold",
        marginTop: 25,
        marginBottom: 10,
      },
      strong: { fontWeight: "bold" },
      em: { fontStyle: "italic" },
      link: { color: colors.tint, textDecorationLine: "underline" },
      paragraph: {
        marginBottom: 8,
      },
      blockquote: {
        backgroundColor:
          colorScheme === "dark"
            ? "rgba(255, 255, 255, 0.05)"
            : "rgba(0, 0, 0, 0.05)",
        borderLeftWidth: 4,
        borderLeftColor:
          colorScheme === "dark"
            ? "rgba(255, 255, 255, 0.3)"
            : "rgba(0, 0, 0, 0.2)",
        paddingLeft: 16,
        paddingVertical: 8,
        marginVertical: 10,
      },
    });
  }, [colors, colorScheme]);

  // MarkdownIt instance that converts all newlines to <br /> for better readability
  const mdInstance = useMemo(
    () => MarkdownIt({ typographer: true, breaks: true }),
    []
  );

  return (
    <ThemedView style={styles.container}>
      <Stack.Screen
        options={{
          headerTitle: t("card.soapNote"),
          headerRight: () => (
            <View style={{ flexDirection: "row", gap: 16 }}>
              <TouchableOpacity onPress={handleCopyClinicalNote}>
                <Ionicons name="copy-outline" size={24} color={colors.text} />
              </TouchableOpacity>
              <TouchableOpacity onPress={handleMenuPress}>
                <Ionicons
                  name="ellipsis-horizontal"
                  size={24}
                  color={colors.text}
                />
              </TouchableOpacity>
            </View>
          ),
        }}
      />

      {isDataLoading ? (
        <ActivityIndicator style={{ marginTop: 40 }} />
      ) : error ? (
        <ThemedText style={{ textAlign: "center", marginTop: 40 }}>
          {t("card.failedToLoadFile")}
        </ThemedText>
      ) : cardFile ? (
        <>
          <ScrollView
            contentContainerStyle={styles.contentContainer}
            showsVerticalScrollIndicator={false}
          >
            {/* Date */}
            <Text style={[styles.dateText, { color: colors.text }]}>
              {formatDateTime(cardFile.createdAt, locale, { format: "long" })}
            </Text>

            {/* Audio Player */}
            <View
              style={[styles.audioPlayer, { backgroundColor: colors.icon }]}
            >
              <TouchableOpacity
                onPress={handlePlayPause}
                style={styles.playButton}
                disabled={isLoading || !sound}
              >
                {isLoading || isBuffering ? (
                  <ActivityIndicator size="small" color={colors.background} />
                ) : (
                  <Ionicons
                    name={isPlaying ? "pause" : "play"}
                    size={32}
                    color={colors.background}
                  />
                )}
              </TouchableOpacity>
              <View style={styles.audioInfo}>
                <Text style={[styles.audioTime, { color: colors.background }]}>
                  {formatTime(position)} / {formatTime(duration)}
                </Text>
                {isBuffering && (
                  <Text
                    style={[styles.bufferingText, { color: colors.background }]}
                  >
                    {t("common.buffering")}
                  </Text>
                )}
              </View>
            </View>

            {/* Status-Dependent Content */}
            {cardFile.status === "completed" &&
              (markdownContent ? (
                <View style={styles.section}>
                  <Markdown style={markdownStyles} markdownit={mdInstance}>
                    {markdownContent}
                  </Markdown>
                </View>
              ) : (
                <View style={styles.statusInfoContainer}>
                  <Ionicons
                    name="document-outline"
                    size={48}
                    style={styles.statusInfoIcon}
                    color={colors.icon}
                  />
                  <Text
                    style={[styles.statusInfoTitle, { color: colors.text }]}
                  >
                    {t("card.noContentTitle")}
                  </Text>
                  <Text
                    style={[styles.statusInfoSubtitle, { color: colors.text }]}
                  >
                    {t("card.noContentSubtitle")}
                  </Text>
                </View>
              ))}

            {(cardFile.status === "pending" ||
              cardFile.status === "processing") && (
              <View style={styles.statusInfoContainer}>
                <ActivityIndicator size="large" color={colors.text} />
                <Text
                  style={[
                    styles.statusInfoTitle,
                    { marginTop: 20, color: colors.text },
                  ]}
                >
                  {t("card.analysisInProgress")}
                </Text>
                <Text
                  style={[styles.statusInfoSubtitle, { color: colors.text }]}
                >
                  {t("card.analysisInProgressSubtitle")}
                </Text>
              </View>
            )}

            {cardFile.status === "failed" && (
              <View style={styles.statusInfoContainer}>
                <Ionicons
                  name="warning-outline"
                  size={48}
                  style={styles.statusInfoIcon}
                  color={colors.danger}
                />
                <Text style={[styles.statusInfoTitle, { color: colors.text }]}>
                  {t("card.analysisFailed")}
                </Text>
                <Text
                  style={[styles.statusInfoSubtitle, { color: colors.text }]}
                >
                  {t("card.analysisFailedSubtitle")}
                </Text>
                <TouchableOpacity
                  style={[
                    styles.reprocessButton,
                    { backgroundColor: colors.tint },
                  ]}
                  onPress={handleReprocess}
                >
                  <Text
                    style={[
                      styles.reprocessButtonText,
                      { color: colors.background },
                    ]}
                  >
                    {t("card.reprocess")}
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </ScrollView>
        </>
      ) : (
        <ThemedText style={{ textAlign: "center", marginTop: 40 }}>
          File not found.
        </ThemedText>
      )}

      <FileOptionsBottomSheet
        ref={bottomSheetModalRef}
        cardId={id as string}
        fileId={fileId as string}
        fileStatus={cardFile?.status}
        onReprocessSuccess={handleReprocessSuccess}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 140,
  },
  dateText: {
    fontSize: 14,
    opacity: 0.8,
    marginBottom: 16,
  },
  audioPlayer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  playButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "rgba(255,255,255,0.3)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  audioInfo: {
    flex: 1,
  },
  audioTime: {
    fontSize: 16,
    fontVariant: ["tabular-nums"],
  },
  bufferingText: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 4,
  },
  section: {
    marginBottom: 24,
  },
  statusInfoContainer: {
    marginTop: 40,
    alignItems: "center",
    paddingHorizontal: 20,
  },
  statusInfoIcon: {
    marginBottom: 16,
  },
  statusInfoTitle: {
    fontSize: 18,
    fontWeight: "600",
    textAlign: "center",
    marginBottom: 8,
  },
  statusInfoSubtitle: {
    fontSize: 15,
    textAlign: "center",
    opacity: 0.7,
    lineHeight: 22,
  },
  reprocessButton: {
    marginTop: 24,
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 8,
  },
  reprocessButtonText: {
    fontSize: 16,
    fontWeight: "bold",
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 12,
  },
  boxContainer: {
    borderRadius: 5,
    padding: 5,
  },
  transcriptContent: {
    fontSize: 16,
    lineHeight: 24,
  },
  soapSection: {
    paddingVertical: 5,
  },
  soapSectionBorder: {
    // borderBottomWidth: 1,
    // borderBottomColor: "rgba(128,128,128,0.1)",
  },
  soapTitle: {
    fontSize: 16,
    marginBottom: 8,
    letterSpacing: 0.3,
  },
  soapContent: {
    fontSize: 16,
    lineHeight: 24,
  },
});
