import { MaterialIcons } from "@expo/vector-icons";
import { BottomSheetModal } from "@gorhom/bottom-sheet";
import { FlashList } from "@shopify/flash-list";
import { useQueryClient } from "@tanstack/react-query";
import { useLocalSearchPara<PERSON>, useRouter } from "expo-router";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { RefreshControl, StyleSheet, Text, View } from "react-native";
import Toast from "react-native-simple-toast";

import { DocumentListSkeleton } from "@/components/skeletons/DocumentListSkeleton";
import { ActionButton } from "@/components/ui/ActionButton";
import { DocumentListItem } from "@/components/ui/DocumentListItem";
import { FileOptionsBottomSheet } from "@/components/ui/FileOptionsBottomSheet";
import { Colors } from "@/constants/Colors";
import { useLanguage, useTranslation } from "@/context/LanguageContext";
import { useColorScheme } from "@/hooks/useColorScheme";
import { useCardFiles } from "@/lib/api/hooks";
import { formatDateTime } from "@/lib/utils/datetime";

interface Document {
  id: string;
  title: string;
  status: "pending" | "processing" | "completed" | "failed";
  dateTime: string;
  icon?: keyof typeof MaterialIcons.glyphMap;
}

const typeToIcon: Record<string, keyof typeof MaterialIcons.glyphMap> = {
  clinical_note: "mic",
  image_analysis: "photo-library",
  journal_research: "search",
  audio_analysis: "headset",
};

export default function FilesScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const { t } = useTranslation();
  const { locale } = useLanguage();
  const queryClient = useQueryClient();

  const [documents, setDocuments] = useState<Document[]>([]);
  const [selectedFileId, setSelectedFileId] = useState<string | null>(null);
  const [selectedFileStatus, setSelectedFileStatus] = useState<
    Document["status"] | null
  >(null);

  // Bottom sheet ref
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);

  // Check if there are any pending or processing files
  const hasPendingFiles = useMemo(() => {
    return documents.some(
      (doc) => doc.status === "pending" || doc.status === "processing"
    );
  }, [documents]);

  // Fetch card files data with automatic refetching for pending/processing files
  const {
    data: cardFilesData,
    isLoading: isFilesLoading,
    isRefetching: isFilesRefetching,
    refetch: refetchFiles,
  } = useCardFiles(id as string);

  // Map API response to Document format
  useEffect(() => {
    if (cardFilesData?.cardFiles) {
      const mapped: Document[] = cardFilesData.cardFiles.map((file) => ({
        id: file.id,
        title:
          file.analysisType === "clinical_note"
            ? t("card.soapNote")
            : file.fileName || file.analysisType,
        status:
          (file.status as "pending" | "processing" | "completed" | "failed") ||
          "completed",
        dateTime: formatDateTime(file.createdAt, locale),
        icon:
          typeToIcon[file.analysisType as keyof typeof typeToIcon] ??
          "insert-drive-file",
      }));
      setDocuments(mapped);
    } else {
      setDocuments([]);
    }
  }, [cardFilesData, locale, t]);

  // Auto-refetch when there are pending or processing files
  useEffect(() => {
    if (hasPendingFiles) {
      console.log("hasPendingFiles", hasPendingFiles);
      const interval = setInterval(async () => {
        // Invalidate the cache to ensure fresh data
        await queryClient.invalidateQueries({ queryKey: ["cardFiles", id] });
        refetchFiles();
      }, 10000); // Refetch every 10 seconds

      return () => clearInterval(interval);
    }
  }, [hasPendingFiles, refetchFiles, queryClient, id]);

  const handleDocumentPress = (doc: Document) => {
    // if (doc.status === "pending" || doc.status === "processing") {
    //   Toast.show(t("card.fileProcessing"), Toast.SHORT, {});
    //   return;
    // }
    // if (doc.status === "failed") {
    //   Toast.show(t("card.fileProcessFailed"), Toast.SHORT, {});
    //   return;
    // }
    router.push(`/cards/${id}/files/${doc.id}` as unknown as any);
  };

  const handleMenuPress = (docId: string) => {
    const doc = documents.find((d) => d.id === docId);
    setSelectedFileId(docId);
    setSelectedFileStatus(doc?.status || null);
    bottomSheetModalRef.current?.present();
  };

  const handleSheetDismiss = useCallback(() => {
    setSelectedFileId(null);
    setSelectedFileStatus(null);
  }, []);

  const handleClinicalNote = useCallback(() => {
    router.push(`/cards/${id}/recording` as unknown as any);
  }, [id, router]);

  const handleImageAnalysis = useCallback(() => {
    Toast.show(t("common.comingSoon"), Toast.SHORT, {});
  }, [t]);

  const handleJournalResearch = useCallback(() => {
    Toast.show(t("common.comingSoon"), Toast.SHORT, {});
  }, [t]);

  const handlePatientFollowup = useCallback(() => {
    Toast.show(t("common.comingSoon"), Toast.SHORT, {});
  }, [t]);

  const handleRefresh = useCallback(() => {
    refetchFiles();
  }, [refetchFiles]);

  // Empty state component
  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <View
        style={[
          styles.iconContainer,
          { backgroundColor: colorScheme === "dark" ? "#1f2937" : "#f3f4f6" },
        ]}
      >
        <MaterialIcons
          name="folder-open"
          size={48}
          color={colorScheme === "dark" ? "#9ca3af" : "#6b7280"}
        />
      </View>
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        {t("card.noFiles")}
      </Text>
      <Text style={[styles.emptySubtitle, { color: colors.text }]}>
        {t("card.createFirstFile")}
      </Text>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.listContainer}>
        {isFilesLoading ? (
          <>
            <View style={styles.actionsSection}>
              <View style={styles.actionsGrid}>
                <View style={styles.actionRow}>
                  <View style={styles.actionButtonWrapper}>
                    <ActionButton
                      title={t("card.clinicalNote")}
                      icon="mic"
                      onPress={handleClinicalNote}
                    />
                  </View>
                  <View style={styles.actionButtonWrapper}>
                    <ActionButton
                      title={t("card.imageAnalysis")}
                      icon="photo-library"
                      onPress={handleImageAnalysis}
                    />
                  </View>
                </View>

                <View style={styles.actionRow}>
                  <View style={styles.actionButtonWrapper}>
                    <ActionButton
                      title={t("card.journalResearch")}
                      icon="search"
                      onPress={handleJournalResearch}
                    />
                  </View>
                  <View style={styles.actionButtonWrapper}>
                    <ActionButton
                      title={t("card.patientFollowup")}
                      icon="headset"
                      onPress={handlePatientFollowup}
                    />
                  </View>
                </View>
              </View>
            </View>
            <DocumentListSkeleton count={4} />
          </>
        ) : (
          <FlashList
            data={documents}
            renderItem={({ item }) => (
              <DocumentListItem
                title={item.title}
                dateTime={item.dateTime}
                icon={item.icon}
                status={item.status}
                onPress={() => handleDocumentPress(item)}
                onMenuPress={() => handleMenuPress(item.id)}
              />
            )}
            ListHeaderComponent={
              <View style={styles.actionsSection}>
                <View style={styles.actionsGrid}>
                  <View style={styles.actionRow}>
                    <View style={styles.actionButtonWrapper}>
                      <ActionButton
                        title={t("card.clinicalNote")}
                        icon="mic"
                        onPress={handleClinicalNote}
                      />
                    </View>
                    <View style={styles.actionButtonWrapper}>
                      <ActionButton
                        title={t("card.imageAnalysis")}
                        icon="photo-library"
                        onPress={handleImageAnalysis}
                      />
                    </View>
                  </View>

                  <View style={styles.actionRow}>
                    <View style={styles.actionButtonWrapper}>
                      <ActionButton
                        title={t("card.journalResearch")}
                        icon="search"
                        onPress={handleJournalResearch}
                      />
                    </View>
                    <View style={styles.actionButtonWrapper}>
                      <ActionButton
                        title={t("card.patientFollowup")}
                        icon="headset"
                        onPress={handlePatientFollowup}
                      />
                    </View>
                  </View>
                </View>
              </View>
            }
            estimatedItemSize={80}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={isFilesRefetching}
                onRefresh={handleRefresh}
                tintColor={colors.text}
              />
            }
            ListEmptyComponent={renderEmptyComponent}
          />
        )}
      </View>

      <FileOptionsBottomSheet
        ref={bottomSheetModalRef}
        cardId={id as string}
        fileId={selectedFileId!}
        fileStatus={selectedFileStatus}
        onDismiss={handleSheetDismiss}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  listContainer: { flex: 1 },
  listContent: { paddingBottom: 100 },
  actionsSection: { paddingHorizontal: 20, paddingTop: 10, paddingBottom: 10 },
  actionsGrid: { gap: 10 },
  actionRow: { flexDirection: "row", gap: 10 },
  actionButtonWrapper: { flex: 1 },
  documentsSection: { flex: 1 },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 80,
    paddingHorizontal: 40,
  },
  iconContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 8,
    textAlign: "center",
  },
  emptySubtitle: {
    fontSize: 15,
    textAlign: "center",
    opacity: 0.6,
    lineHeight: 22,
  },
});
