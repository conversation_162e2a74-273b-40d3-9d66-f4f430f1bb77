import { MaterialIcons } from "@expo/vector-icons";
import { Tabs } from "expo-router";
import React from "react";
import { Platform } from "react-native";

import { HapticTab } from "@/components/HapticTab";
import TabBarBackground from "@/components/ui/TabBarBackground";
import { Colors } from "@/constants/Colors";
import { useTranslation } from "@/context/LanguageContext";
import { useColorScheme } from "@/hooks/useColorScheme";

export default function CardTabLayout() {
  const colorScheme = useColorScheme();
  const { t } = useTranslation();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors[colorScheme ?? "light"].tint,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            position: "absolute",
          },
          default: {},
        }),
      }}
    >
      <Tabs.Screen
        name="files"
        options={{
          title: t("card.files"),
          tabBarIcon: ({ color }) => (
            <MaterialIcons size={28} name="folder" color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="ai-assistant"
        options={{
          title: t("card.aiAssistant"),
          tabBarIcon: ({ color }) => (
            <MaterialIcons size={28} name="message" color={color} />
          ),
        }}
      />
    </Tabs>
  );
}
