import { MaterialIcons } from "@expo/vector-icons";
import BottomSheet from "@gorhom/bottom-sheet";
import { router, Stack } from "expo-router";
import React, { useRef } from "react";
import { ScrollView, StyleSheet, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import { ProfileSkeleton } from "@/components/skeletons/ProfileSkeleton";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Button } from "@/components/ui/Button";
import { LanguageSelector } from "@/components/ui/LanguageSelector";
import { Colors } from "@/constants/Colors";
import { useAuth } from "@/context/AuthContext";
import { useLanguage } from "@/context/LanguageContext";
import { useColorScheme } from "@/hooks/useColorScheme";
import { useProfile } from "@/lib/api/hooks";

export default function ProfileScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const { signOut, isLoading } = useAuth();
  const { locale, t, isSystemLanguage } = useLanguage();
  const languageSelectorRef = useRef<BottomSheet>(null);

  const { data: profileData } = useProfile();
  const user = profileData?.user || null;

  const handleLogout = async () => {
    await signOut();
  };

  const handleBack = () => {
    router.back();
  };

  const handleLanguagePress = () => {
    languageSelectorRef.current?.expand();
  };

  const handleLanguageSelectorClose = () => {
    languageSelectorRef.current?.close();
  };

  if (isLoading) {
    return (
      <SafeAreaView
        style={[styles.container, { backgroundColor: colors.background }]}
      >
        <Stack.Screen
          options={{
            headerShown: true,
            headerTitle: "Profile",
            headerLeft: () => (
              <TouchableOpacity onPress={handleBack} style={{ marginLeft: 10 }}>
                <MaterialIcons
                  name="chevron-left"
                  size={24}
                  color={colors.tint}
                />
              </TouchableOpacity>
            ),
            headerStyle: {
              backgroundColor: colors.background,
            },
            headerShadowVisible: true,
          }}
        />
        <ProfileSkeleton />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background }]}
    >
      <Stack.Screen options={{ headerTitle: t("profile.title") }} />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.profileHeader}>
          <View
            style={[
              styles.avatarContainer,
              { backgroundColor: colors.tint + "20" },
            ]}
          >
            <MaterialIcons name="person" size={60} color={colors.tint} />
          </View>

          {user && (
            <>
              <ThemedText type="title" style={styles.userName}>
                {user.firstName} {user.lastName}
              </ThemedText>
              <ThemedText type="default" style={styles.userEmail}>
                {user.email}
              </ThemedText>
              <View
                style={[
                  styles.statusBadge,
                  { backgroundColor: colors.tint + "20" },
                ]}
              >
                <ThemedText style={[styles.statusText, { color: colors.tint }]}>
                  {user.status.toUpperCase()}
                </ThemedText>
              </View>
            </>
          )}
        </View>

        {/* <ThemedView style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            {t("profile.accountInformation")}
          </ThemedText>

          <View style={styles.infoRow}>
            <ThemedText style={styles.infoLabel}>
              {t("profile.name")}
            </ThemedText>
            <ThemedText style={styles.infoValue}>
              {user?.firstName} {user?.lastName}
            </ThemedText>
          </View>

          <View style={styles.infoRow}>
            <ThemedText style={styles.infoLabel}>
              {t("profile.email")}
            </ThemedText>
            <ThemedText style={styles.infoValue}>{user?.email}</ThemedText>
          </View>

          <View style={styles.infoRow}>
            <ThemedText style={styles.infoLabel}>
              {t("profile.status")}
            </ThemedText>
            <ThemedText style={styles.infoValue}>
              {user?.status || "Unknown"}
            </ThemedText>
          </View>
        </ThemedView> */}

        <ThemedView style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            {t("common.settings")}
          </ThemedText>

          <TouchableOpacity
            style={styles.settingRow}
            onPress={handleLanguagePress}
          >
            <View style={styles.settingLeft}>
              <MaterialIcons name="language" size={24} color={colors.text} />
              <ThemedText style={styles.settingLabel}>
                {t("profile.language")}
              </ThemedText>
            </View>
            <View style={styles.settingRight}>
              <ThemedText style={styles.settingValue}>
                {isSystemLanguage
                  ? t("profile.system")
                  : locale === "en"
                    ? t("profile.english")
                    : t("profile.chinese")}
              </ThemedText>
              <MaterialIcons
                name="chevron-right"
                size={24}
                color={colors.icon}
              />
            </View>
          </TouchableOpacity>
        </ThemedView>

        <View style={styles.logoutContainer}>
          <Button
            title={t("common.logout")}
            onPress={handleLogout}
            variant="secondary"
            containerStyle={styles.logoutButton}
          />
        </View>
      </ScrollView>

      <LanguageSelector
        ref={languageSelectorRef}
        onClose={handleLanguageSelectorClose}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  profileHeader: {
    alignItems: "center",
    paddingVertical: 32,
    paddingHorizontal: 20,
  },
  avatarContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
  },
  userName: {
    fontSize: 24,
    fontWeight: "700",
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 16,
    opacity: 0.7,
    marginBottom: 12,
  },
  statusBadge: {
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 20,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "600",
  },
  section: {
    marginHorizontal: 20,
    marginBottom: 24,
    padding: 20,
    borderRadius: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: "#e5e7eb33",
  },
  infoLabel: {
    fontSize: 14,
    opacity: 0.7,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: "500",
  },
  logoutContainer: {
    paddingHorizontal: 20,
    marginTop: 20,
  },
  logoutButton: {
    borderColor: "#ef4444",
  },
  settingRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: "#e5e7eb33",
  },
  settingLeft: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  settingRight: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  settingLabel: {
    fontSize: 16,
  },
  settingValue: {
    fontSize: 14,
    opacity: 0.7,
  },
});
