# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

You run in an environment where `ast-grep` is available; whenever a search requires syntax-aware or structural matching, default to `ast-grep --lang rust -p '<pattern>'` (or set `--lang` appropriately) and avoid falling back to text-only tools like `rg` or `grep` unless I explicitly request a plain-text search.

## Project Overview

Scribe App is a medical record management application for AI MOVE. It enables healthcare professionals to manage medical cards (visits/cases), documents, and interact with AI assistance for clinical tasks.

**IMPORTANT**: The codebase is currently in a transitional state with incomplete refactoring from "patient" to "cards" terminology. While routes and API have been updated, many UI components and text still use "patient" terminology.

## Essential Commands

### Development

- `npm install` - Install dependencies
- `npm start` - Start Expo development server
- `npm run ios` - Run on iOS simulator
- `npm run android` - Run on Android emulator
- `npm run web` - Run web version
- `npm run lint` - Run ESLint (uses expo lint)
- `npm run typecheck` - Run TypeScript type checking (**MUST RUN after every code change**)
- `npm run reset-project` - Reset to blank project state (interactive)

### Testing

**Note**: No testing framework is currently configured. Consider setting up Jest and React Native Testing Library.

## Key Dependencies

- **UI/UX**: `react-native-svg`, `@gorhom/bottom-sheet`, `expo-haptics`, `expo-image`, `@shopify/flash-list`, `@expo/react-native-action-sheet`, `expo-blur`
- **Navigation**: `expo-router` (file-based routing)
- **Gestures/Animation**: `react-native-gesture-handler`, `react-native-reanimated`
- **Audio/Video**: `expo-audio`, `expo-av`
- **Internationalization**: `i18n-js` with `expo-localization`
- **Markdown**: `@ronradtke/react-native-markdown-display` (for AI chat responses and SOAP notes)
- **Web Support**: `react-native-webview`
- **State Management**: React Context + React Query
- **API Client**: `@tanstack/react-query` with Fetch API
- **Secure Storage**: `expo-secure-store` for secure token persistence
- **Toast Notifications**: `react-native-simple-toast`
- **Utilities**: `dayjs` (date/time), `expo-clipboard` (clipboard access)

## API Integration

### Configuration

- API URL configured via environment variable: `EXPO_PUBLIC_API_DOMAIN`
- Default: `http://localhost:3000`
- Create `.env` file from `.env.example` to set custom API URL

### API Endpoints

#### Authentication & User
1. **Login**: POST `/v1/login` with email/password
2. **Signup**: POST `/v1/register` with email/password/firstName/lastName
3. **Profile**: GET `/v1/profile` (requires authentication)

#### Cards Management
4. **List Cards**: GET `/v1/cards` (list all cards)
5. **Card Detail**: GET `/v1/card/${id}` (single card)
6. **Create Card**: POST `/v1/card` with card data
7. **Delete Card**: DELETE `/v1/card/${id}` (delete a card)

#### Files & Analysis
8. **List Card Files**: GET `/v1/card/${cardId}/files`
9. **Analyze File**: POST `/v1/card/${cardId}/analyze-file` (multipart upload with analysisType)
10. **Get File Detail**: GET `/v1/card-file/${fileId}` (includes contexts)
11. **Regenerate File**: POST `/v1/card-file/${cardFileId}/regenerate` (reprocess file analysis)

#### Speech & AI
12. **Speech-to-Text**: POST `/v1/speechtotext` (multipart audio upload)
13. **Get Conversations**: GET `/v1/card/${cardId}/llm/conversations/messages?limit=${limit}`
14. **Send Chat Message**: POST `/v1/card/${cardId}/llm/chat-message` with query and optional conversation_id
15. **Clear Conversation**: DELETE `/v1/card/${cardId}/llm/chat-message` (clear chat history)

### API Client Architecture

- **Location**: `/lib/api/`
  - `types.ts`: TypeScript interfaces for API requests/responses
  - `hooks.ts`: React Query hooks for API calls
- **Token Management**: Secure token storage using expo-secure-store with expiry tracking
- **Error Handling**: Consistent error messages with user alerts
- **Query Invalidation**: Automatic profile refresh after login/signup

### Usage Example

```typescript
import { useLogin, useProfile } from "@/lib/api/hooks";

// In component
const loginMutation = useLogin();
const { data: profileData, isLoading } = useProfile();

// Login
await loginMutation.mutateAsync({ email, password });
```

## Architecture Overview

### Navigation Flow

The app uses Expo Router with file-based routing and a multi-level navigation structure:

1. **Root Layout** (`app/_layout.tsx`):

   - Wraps app in AuthProvider, GestureHandlerRootView, and BottomSheetModalProvider
   - Implements route protection using `Stack.Protected`
   - Unauthenticated routes: `/login`, `/sign-up`
   - Authenticated routes: `/(app)/*`
   - Index route (`app/index.tsx`) redirects based on auth status

2. **Authentication Flow**:

   - **Login Screen** (`app/login.tsx`): Email/password login with social options
   - **Sign Up Screen** (`app/sign-up.tsx`): Registration with terms acceptance
   - Both screens feature AI MOVE branding and navigate to home on success

3. **Main App Navigation** (`app/(app)/_layout.tsx`):

   - Stack navigation (not tabs) with main screens:
   - **Home Screen** (`app/(app)/home.tsx`): Cards list view (displays medical cards/visits)
   - **Card Detail** (`app/(app)/cards/[id]/*`): Tab-based card view
   - **Recording Screen** (`app/(app)/cards/[id]/recording.tsx`): Modal for voice recording
   - **File Detail** (`app/(app)/cards/[id]/files/[fileId].tsx`): File detail view with audio playback

4. **Card Detail Tabs** (`app/(app)/cards/[id]/(tabs)/_layout.tsx`):
   - **Files Tab**: Document management and creation actions
   - **AI Assistant Tab**: AI-powered clinical assistance with chat interface

### Component Library

The app includes a comprehensive UI component library:

**Core UI Components** (`components/ui/`):

- **Logo**: SVG-based AI MOVE logo component
- **Input**: Text input with password visibility toggle
- **Button**: Primary/secondary variants with loading states
- **SocialButton**: OAuth provider buttons (Google, Apple, WeChat)
- **Checkbox**: Custom checkbox with theme support
- **PatientVisitCard**: Card list item with avatar and menu (note: still uses "patient" naming)
- **DocumentListItem**: Document display with like/notification features
- **ActionButton**: Medical action buttons with icons
- **Dropdown**: Filter/sort dropdown with modal selection
- **IconSymbol**: Platform-specific icon component (iOS variant available)
- **TabBarBackground**: Tab bar background (iOS-specific styling)
- **FileOptionsBottomSheet**: Bottom sheet for file options (reprocess functionality)
- **LanguageSelector**: Language selection component
- **KeyboardPaddingView**: Keyboard padding component
- **Skeleton**: Base skeleton component for loading states

**Layout Components** (`components/`):

- **Collapsible**: Animated collapsible container
- **ExternalLink**: External link component with proper handling
- **HapticTab**: Tab button with haptic feedback
- **HelloWave**: Animated wave gesture component
- **ParallaxScrollView**: Parallax scrolling container
- **ThemedText**: Theme-aware text component
- **ThemedView**: Theme-aware view container

### Key Technical Details

- **TypeScript**: Strict mode enabled with path alias `@/*` mapping to root
- **Styling**: Theme-aware with automatic dark/light mode switching
- **Icons**: Uses SF Symbols via IconSymbol component (iOS-specific implementation)
- **SVG Support**: react-native-svg for logo and graphics rendering
- **New Architecture**: React Native's new architecture is enabled
- **Bundle IDs**: `com.aimove.scribe` (iOS/Android)
- **Deep Linking**: Scheme configured as `scribeapp`
- **Version**: 0.1.0
- **Expo SDK**: 53.0.10
- **Experiments**: `typedRoutes` enabled for type-safe routing
- **Android**: Edge-to-edge mode enabled

### Project Structure

```
app/
├── _layout.tsx                    # Root layout with auth protection
├── index.tsx                      # Auth redirect logic
├── login.tsx                      # Login screen
├── sign-up.tsx                    # Sign up screen
├── +not-found.tsx                 # 404 error page
├── (app)/                         # Protected app routes
│   ├── _layout.tsx                # Stack navigator
│   ├── home.tsx                   # Cards list screen
│   ├── profile.tsx                # User profile screen
│   └── cards/
│       └── [id]/
│           ├── recording.tsx      # Voice recording modal
│           ├── files/
│           │   └── [fileId].tsx   # File detail view
│           └── (tabs)/
│               ├── _layout.tsx    # Tab navigator
│               ├── files.tsx      # Documents tab
│               └── ai-assistant.tsx # AI chat tab
components/
├── ui/                            # Core UI components
│   ├── Logo.tsx                   # AI MOVE logo
│   ├── Input.tsx                  # Form inputs
│   ├── Button.tsx                 # Action buttons
│   ├── SocialButton.tsx           # OAuth buttons
│   ├── Checkbox.tsx               # Checkboxes
│   ├── PatientVisitCard.tsx       # Card list items (needs renaming)
│   ├── DocumentListItem.tsx       # Document items
│   ├── ActionButton.tsx           # Medical actions
│   ├── Dropdown.tsx               # Filter/sort
│   ├── IconSymbol.tsx             # Icons
│   ├── TabBarBackground.tsx       # Tab styling
│   ├── FileOptionsBottomSheet.tsx # File options sheet
│   ├── LanguageSelector.tsx       # Language picker
│   ├── KeyboardPaddingView.tsx    # Keyboard padding
│   └── Skeleton.tsx               # Base skeleton
├── Collapsible.tsx                # Animated sections
├── ExternalLink.tsx               # External links
├── HapticTab.tsx                  # Haptic feedback tabs
├── HelloWave.tsx                  # Wave animation
├── ParallaxScrollView.tsx         # Parallax containers
├── ThemedText.tsx                 # Theme-aware text
├── ThemedView.tsx                 # Theme-aware views
└── skeletons/                     # Loading states
    ├── PatientListSkeleton.tsx    # Cards list loading (needs renaming)
    ├── PatientCardSkeleton.tsx    # Card item loading (needs renaming)
    ├── DocumentListSkeleton.tsx   # Documents loading
    ├── ProfileSkeleton.tsx        # Profile loading
    └── ActionButtonsSkeleton.tsx  # Action buttons loading
context/
├── AuthContext.tsx                # Authentication state + API integration
└── LanguageContext.tsx            # i18n language switching
lib/
├── api/                           # API integration
│   ├── types.ts                   # TypeScript interfaces
│   └── hooks.ts                   # React Query hooks
├── i18n/                          # Internationalization
│   ├── index.ts                   # i18n configuration
│   └── locales/                   # Translation files
│       ├── en.json                # English translations
│       └── zh.json                # Chinese translations
└── providers/
    └── query-client.tsx           # React Query provider
```

### Development Considerations

0. **IMPORTANT - Incomplete Refactoring**:

   - The codebase is transitioning from "patient" to "cards" terminology
   - Routes and API endpoints use "cards"
   - Many UI components and text still use "patient" (e.g., PatientVisitCard, "New Patient Visit")
   - Variable names and functions may still reference "patient"
   - This inconsistency should be addressed for clarity

1. **Authentication**: Now integrated with real API:

   - Uses React Query for API calls
   - Token stored securely using expo-secure-store with expiry tracking
   - Profile automatically fetched when authenticated
   - Proper error handling with user feedback
   - Loading states managed by React Query

2. **Navigation**:

   - Home screen is not in tabs (standalone)
   - Only card detail view has tab navigation
   - Dynamic routing with card IDs
   - Back navigation from card detail to home
   - Recording screen opens as modal presentation

3. **Data Management**:

   - Now integrated with real API for cards data
   - Using React Query for server state management
   - Local state management via React hooks (no Redux/MobX)
   - Type-safe API client with TypeScript interfaces

4. **Medical Features**:

   - **Clinical Note Recording**: ✅ Fully implemented with audio recording, speech-to-text, and file analysis
   - **AI Assistant**: ✅ Chat interface with streaming responses and markdown rendering
   - **File Management**: ✅ File detail view with audio playback and SOAP note display
   - **File Reprocessing**: ✅ Regenerate file analysis with different options
   - **Copy to Clipboard**: ✅ Copy clinical notes to clipboard
   - **Card Deletion**: ✅ Delete cards with confirmation
   - **Chat Management**: ✅ Clear conversation history
   - **Image Analysis**: Planned integration
   - **Journal Research**: Planned functionality
   - **Patient Followup**: Planned scheduling feature

5. **Recording Feature**:

   - ✅ Full audio recording with expo-audio
   - ✅ Animated waveform visualization
   - ✅ Timer and controls (start/pause/resume/stop)
   - ✅ Modal presentation from card detail
   - ✅ Speech-to-text integration
   - ✅ File analysis with status tracking

6. **Platform Considerations**:
   - iOS-specific components for icons and tab bar
   - Haptic feedback integration
   - Bottom sheet modals support
   - Gesture handling configured
   - Audio format support: .wav and .m4a
   - File uploads with presigned URLs

7. **Internationalization**:
   - English and Chinese language support
   - Language switcher in profile settings
   - Persistent language preference using SecureStore
   - Date/time formatting localized
   - Toast messages internationalized

## Additional Rules

1. ScrollView is fine for most cases, only use FlatList when recycling.
2. Prefer CSS boxShadow to RN legacy shadow props.

## Code Quality Requirements

1. **ALWAYS run `npm run typecheck` after making any code changes** to ensure TypeScript type safety
2. **ALWAYS run `npm run lint` before committing** to maintain code quality
3. Fix all type errors and lint warnings before considering a task complete
