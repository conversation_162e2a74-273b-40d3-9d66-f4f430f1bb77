import * as SecureStore from "expo-secure-store";
import React, { useEffect, useState } from "react";

import { queryClient } from "@/lib/providers/query-client";

interface AuthContextType {
  session: string | null;
  isLoading: boolean;
  signIn: (token: string, expiresInSeconds: number) => Promise<void>;
  signOut: () => Promise<void>;
}

export const TOKEN_KEY = "scribe_auth_token";
export const EXPIRY_KEY = "scribe_token_expiry";

const AuthContext = React.createContext<AuthContextType>({
  session: null,
  isLoading: true,
  signIn: async () => {},
  signOut: async () => {},
});

// This hook can be used to access the user info.
export function useAuth() {
  return React.useContext(AuthContext);
}

// Minimal auth event bus (no external deps)
const logoutListeners = new Set<() => void>();

export function triggerSignOut() {
  logoutListeners.forEach((fn) => fn());
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<string | null>(null);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  // Check for stored token on mount
  useEffect(() => {
    const checkStoredAuth = async () => {
      try {
        const token = await SecureStore.getItemAsync(TOKEN_KEY);
        if (token) {
          setSession(token);
        }
      } catch (error) {
        console.error("Error checking stored auth:", error);
      } finally {
        setIsCheckingAuth(false);
      }
    };

    checkStoredAuth();
  }, []);

  const signIn = async (token: string, expiresInSeconds: number) => {
    try {
      setSession(token);
      await SecureStore.setItemAsync(TOKEN_KEY, token);
      await SecureStore.setItemAsync(EXPIRY_KEY, expiresInSeconds.toString());
    } catch (error) {
      console.error("Error storing auth token:", error);
    }
  };

  const signOut = async () => {
    console.log("Signing out");
    setSession(null);
    await SecureStore.deleteItemAsync(TOKEN_KEY);
    await SecureStore.deleteItemAsync(EXPIRY_KEY);
    // Clear all React Query cache
    queryClient.clear();
  };

  // Subscribe to global logout events
  useEffect(() => {
    logoutListeners.add(signOut);
    return () => {
      logoutListeners.delete(signOut);
    };
  }, []);

  return (
    <AuthContext.Provider
      value={{
        session,
        isLoading: isCheckingAuth,
        signIn,
        signOut,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}
