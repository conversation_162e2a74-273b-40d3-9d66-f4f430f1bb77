import React, { createContext, useContext, useEffect, useState } from "react";

import i18n, {
  initializeI18n,
  changeLanguage,
  currentLocale,
  isUsingSystemLanguage,
} from "@/lib/i18n";

interface LanguageContextType {
  locale: string;
  setLanguage: (language: "en" | "zh" | "system") => Promise<void>;
  t: typeof i18n.t;
  isLoading: boolean;
  isSystemLanguage: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(
  undefined
);

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [locale, setLocale] = useState<string>("en");
  const [isLoading, setIsLoading] = useState(true);
  const [isSystemLanguage, setIsSystemLanguage] = useState(false);

  useEffect(() => {
    // Initialize i18n on mount
    initializeI18n()
      .then(async () => {
        setLocale(currentLocale());
        setIsSystemLanguage(await isUsingSystemLanguage());
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  const setLanguage = async (language: "en" | "zh" | "system") => {
    await changeLanguage(language);
    setLocale(currentLocale());
    setIsSystemLanguage(language === "system");
  };

  return (
    <LanguageContext.Provider
      value={{
        locale,
        setLanguage,
        t: i18n.t.bind(i18n),
        isLoading,
        isSystemLanguage,
      }}
    >
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
}

// Convenience hook for translations
export function useTranslation() {
  const { t } = useLanguage();
  return { t };
}
