import React from "react";
import { StyleSheet, View } from "react-native";

import { Skeleton } from "./Skeleton";

import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";

export const PatientCardSkeleton: React.FC = () => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  return (
    <View
      style={[
        styles.card,
        {
          backgroundColor: colors.card,
          borderColor: colorScheme === "dark" ? "#374151" : "#e5e7eb",
        },
      ]}
    >
      <Skeleton variant="circular" width={40} height={40} />
      <View style={styles.content}>
        <Skeleton width="60%" height={18} style={styles.title} />
        <Skeleton width="40%" height={14} />
      </View>
      <Skeleton width={24} height={24} borderRadius={12} />
    </View>
  );
};

interface PatientListSkeletonProps {
  count?: number;
}

export const PatientListSkeleton: React.FC<PatientListSkeletonProps> = ({
  count = 3,
}) => {
  return (
    <View style={styles.container}>
      {Array.from({ length: count }).map((_, index) => (
        <PatientCardSkeleton key={index} />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { marginHorizontal: 20 },
  card: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
  },
  content: {
    flex: 1,
    marginLeft: 16,
  },
  title: {
    marginBottom: 8,
  },
});
