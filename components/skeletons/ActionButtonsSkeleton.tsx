import React from "react";
import { StyleSheet, View } from "react-native";

import { Skeleton } from "./Skeleton";

interface ActionButtonsSkeletonProps {
  style?: any;
}

export const ActionButtonsSkeleton: React.FC<ActionButtonsSkeletonProps> = ({
  style,
}) => {
  return (
    <View style={[styles.container, style]}>
      <View style={styles.row}>
        <View style={styles.buttonWrapper}>
          <Skeleton width="100%" height={80} borderRadius={12} />
        </View>
        <View style={styles.buttonWrapper}>
          <Skeleton width="100%" height={80} borderRadius={12} />
        </View>
      </View>
      <View style={styles.row}>
        <View style={styles.buttonWrapper}>
          <Skeleton width="100%" height={80} borderRadius={12} />
        </View>
        <View style={styles.buttonWrapper}>
          <Skeleton width="100%" height={80} borderRadius={12} />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  row: {
    flexDirection: "row",
    gap: 14,
    marginBottom: 14,
  },
  buttonWrapper: {
    flex: 1,
  },
});
