import React, { useEffect, useRef } from "react";
import {
  Animated,
  DimensionValue,
  StyleProp,
  StyleSheet,
  View,
  ViewStyle,
} from "react-native";

import { useColorScheme } from "@/hooks/useColorScheme";

interface SkeletonProps {
  width?: DimensionValue;
  height?: DimensionValue;
  borderRadius?: number;
  style?: StyleProp<ViewStyle>;
  variant?: "text" | "circular" | "rectangular";
  animation?: "pulse" | "wave" | "none";
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = "100%",
  height = 20,
  borderRadius,
  style,
  variant = "rectangular",
  animation = "wave",
}) => {
  const colorScheme = useColorScheme();
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (animation === "none") return;

    const animate = () => {
      if (animation === "pulse") {
        Animated.sequence([
          Animated.timing(animatedValue, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(animatedValue, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]).start(() => animate());
      } else if (animation === "wave") {
        Animated.loop(
          Animated.timing(animatedValue, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          }),
        ).start();
      }
    };

    animate();
  }, [animatedValue, animation]);

  const getRadius = () => {
    if (borderRadius !== undefined) return borderRadius;
    switch (variant) {
      case "circular":
        return typeof height === "number" ? height / 2 : 50;
      case "text":
        return 4;
      default:
        return 8;
    }
  };

  const backgroundColor = colorScheme === "dark" ? "#374151" : "#e5e7eb";
  const shimmerColor = colorScheme === "dark" ? "#4b5563" : "#f3f4f6";

  const opacity = animatedValue.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [1, 0.3, 1],
  });

  const translateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [-300, 300],
  });

  return (
    <View
      style={[
        {
          width,
          height,
          borderRadius: getRadius(),
          backgroundColor,
          overflow: "hidden",
        },
        style,
      ]}
    >
      {animation === "pulse" && (
        <Animated.View
          style={[
            StyleSheet.absoluteFillObject,
            {
              backgroundColor,
              opacity,
            },
          ]}
        />
      )}
      {animation === "wave" && (
        <Animated.View
          style={[
            StyleSheet.absoluteFillObject,
            {
              backgroundColor: shimmerColor,
              transform: [{ translateX }],
            },
          ]}
        >
          <View
            style={[
              StyleSheet.absoluteFillObject,
              {
                backgroundColor: shimmerColor,
                opacity: 0.5,
              },
            ]}
          />
        </Animated.View>
      )}
    </View>
  );
};

interface SkeletonContainerProps {
  children: React.ReactNode;
  isLoading: boolean;
  skeleton?: React.ReactElement;
}

export const SkeletonContainer: React.FC<SkeletonContainerProps> = ({
  children,
  isLoading,
  skeleton,
}) => {
  if (isLoading) {
    return skeleton || <Skeleton />;
  }
  return <>{children}</>;
};
