import React from "react";
import { StyleSheet, View } from "react-native";

import { Skeleton } from "./Skeleton";

import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";

export const ProfileSkeleton: React.FC = () => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  return (
    <View style={styles.container}>
      {/* Profile Header */}
      <View style={styles.header}>
        <Skeleton variant="circular" width={120} height={120} />
        <Skeleton width={200} height={24} style={styles.name} />
        <Skeleton width={180} height={16} style={styles.email} />
        <Skeleton width={80} height={32} borderRadius={16} />
      </View>

      {/* Account Information Section */}
      <View
        style={[
          styles.section,
          {
            backgroundColor: colors.card,
            borderColor: colorScheme === "dark" ? "#374151" : "#e5e7eb",
          },
        ]}
      >
        <Skeleton width={150} height={20} style={styles.sectionTitle} />

        {/* Info Rows */}
        {[1, 2, 3].map((index) => (
          <View key={index} style={styles.infoRow}>
            <Skeleton width={60} height={14} />
            <Skeleton width={120} height={14} />
          </View>
        ))}
      </View>

      {/* Logout Button */}
      <View style={styles.logoutContainer}>
        <Skeleton width="100%" height={48} borderRadius={8} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    alignItems: "center",
    paddingVertical: 32,
    paddingHorizontal: 20,
  },
  name: {
    marginTop: 16,
    marginBottom: 8,
  },
  email: {
    marginBottom: 12,
  },
  section: {
    marginHorizontal: 20,
    marginBottom: 24,
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 12,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: "#e5e7eb33",
  },
  logoutContainer: {
    paddingHorizontal: 20,
    marginTop: 20,
  },
});
