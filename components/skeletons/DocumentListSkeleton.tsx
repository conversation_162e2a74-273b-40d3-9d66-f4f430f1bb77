import React from "react";
import { StyleSheet, View } from "react-native";
import { Skeleton } from "./Skeleton";

import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";

export const DocumentCardSkeleton: React.FC = () => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  return (
    <View
      style={[
        styles.card,
        {
          backgroundColor: colorScheme === "dark" ? "#1f2937" : "#ffffff",
          borderColor: colorScheme === "dark" ? "#374151" : "#e5e7eb",
        },
      ]}
    >
      <View style={styles.contentContainer}>
        {/* Icon */}
        <Skeleton width={24} height={24} borderRadius={4} style={styles.icon} />

        {/* Text content */}
        <View style={styles.textContainer}>
          {/* Title */}
          <Skeleton width="60%" height={16} style={styles.titleSkeleton} />

          {/* Bottom row: date and status */}
          <View style={styles.bottomRow}>
            <Skeleton width="35%" height={13} />
            <View style={styles.statusContainer}>
              <Skeleton
                width={14}
                height={14}
                borderRadius={7}
                style={styles.statusIcon}
              />
              <Skeleton width={50} height={12} />
            </View>
          </View>
        </View>

        {/* Action buttons */}
        <View style={styles.actionsContainer}>
          <Skeleton width={20} height={20} borderRadius={10} />
        </View>
      </View>
    </View>
  );
};

interface DocumentListSkeletonProps {
  count?: number;
}

export const DocumentListSkeleton: React.FC<DocumentListSkeletonProps> = ({
  count = 3,
}) => {
  return (
    <View style={styles.container}>
      {Array.from({ length: count }).map((_, index) => (
        <DocumentCardSkeleton key={index} />
      ))}
    </View>
  );
};

export const ActionButtonsSkeleton: React.FC = () => {
  return (
    <View style={styles.actionButtonsContainer}>
      <View style={styles.actionButtonsRow}>
        {[1, 2].map((index) => (
          <View key={index} style={styles.actionButtonWrapper}>
            <Skeleton width="100%" height={80} borderRadius={12} />
          </View>
        ))}
      </View>
      <View style={styles.actionButtonsRow}>
        {[3, 4].map((index) => (
          <View key={index} style={styles.actionButtonWrapper}>
            <Skeleton width="100%" height={80} borderRadius={12} />
          </View>
        ))}
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    // No padding here as cards have their own margins
  },
  card: {
    paddingVertical: 10,
    paddingHorizontal: 10,
    borderWidth: StyleSheet.hairlineWidth,
    marginHorizontal: 20,
    marginBottom: 5,
    borderRadius: 10,
  },
  contentContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  icon: {
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
    marginRight: 16,
  },
  titleSkeleton: {
    marginBottom: 8,
  },
  bottomRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusIcon: {
    marginRight: 4,
  },
  actionsContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  actionButtonsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  actionButtonsRow: {
    flexDirection: "row",
    marginBottom: 12,
  },
  actionButtonWrapper: {
    flex: 1,
    marginHorizontal: 6,
  },
});
