import { MaterialIcons } from "@expo/vector-icons";
import React from "react";
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

import { Colors } from "@/constants/Colors";
import { useTranslation } from "@/context/LanguageContext";
import { useColorScheme } from "@/hooks/useColorScheme";

interface DocumentListItemProps {
  title: string;
  subtitle?: string;
  dateTime: string;
  hasNotification?: boolean;
  isLiked?: boolean;
  onPress?: () => void;
  onMenuPress?: () => void;
  onLikePress?: () => void;
  icon?: keyof typeof MaterialIcons.glyphMap;
  status?: "pending" | "processing" | "completed" | "failed";
}

export function DocumentListItem({
  title,
  dateTime,
  hasNotification = false,
  isLiked = false,
  onPress,
  onMenuPress,
  onLikePress,
  icon,
  status = "completed",
}: DocumentListItemProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const { t } = useTranslation();

  const getStatusColor = () => {
    switch (status) {
      case "pending":
        return "#f59e0b"; // amber
      case "processing":
        return "#3b82f6"; // blue
      case "completed":
        return "#10b981"; // green
      case "failed":
        return "#ef4444"; // red
      default:
        return colors.text;
    }
  };

  const getStatusIcon = (): keyof typeof MaterialIcons.glyphMap => {
    switch (status) {
      case "pending":
        return "schedule";
      case "processing":
        return "sync";
      case "completed":
        return "check-circle";
      case "failed":
        return "error";
      default:
        return "check-circle";
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: colorScheme === "dark" ? "#1f2937" : "#ffffff",
          borderColor: colorScheme === "dark" ? "#374151" : "#e5e7eb",
        },
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.contentContainer}>
        {icon && (
          <MaterialIcons
            name={icon}
            size={24}
            color={colorScheme === "dark" ? "#9ca3af" : "#6b7280"}
            style={{ marginRight: 12 }}
          />
        )}
        <View style={styles.textContainer}>
          <View style={styles.titleRow}>
            <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
          </View>
          <View style={styles.bottomRow}>
            <Text style={[styles.dateTime, { color: colors.text }]}>
              {dateTime}
            </Text>
            <View style={styles.statusContainer}>
              {status === "processing" ? (
                <ActivityIndicator
                  size="small"
                  color={getStatusColor()}
                  style={styles.statusIcon}
                />
              ) : (
                <MaterialIcons
                  name={getStatusIcon()}
                  size={14}
                  color={getStatusColor()}
                  style={styles.statusIcon}
                />
              )}
              <Text style={[styles.statusText, { color: getStatusColor() }]}>
                {t(`status.${status}`)}
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={onMenuPress}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <MaterialIcons
              name="more-horiz"
              size={20}
              color={colorScheme === "dark" ? "#9ca3af" : "#6b7280"}
            />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 10,
    paddingHorizontal: 10,
    borderWidth: StyleSheet.hairlineWidth,
    marginHorizontal: 20,
    marginBottom: 5,
    borderRadius: 10,
  },
  contentContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  textContainer: {
    flex: 1,
    marginRight: 16,
  },
  titleRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  title: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    marginBottom: 4,
    opacity: 0.8,
  },
  dateTime: {
    fontSize: 13,
    opacity: 0.6,
    flexShrink: 1,
    marginRight: 8,
  },
  notificationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#3b82f6",
    marginLeft: 8,
  },
  actionsContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  actionButton: {
    padding: 4,
    marginLeft: 12,
  },
  bottomRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusIcon: {
    marginRight: 4,
  },
  statusText: {
    fontSize: 11,
    fontWeight: "500",
  },
});
