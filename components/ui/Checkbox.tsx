import { MaterialIcons } from "@expo/vector-icons";
import React from "react";
import { StyleSheet, TouchableOpacity, ViewStyle } from "react-native";

import { useColorScheme } from "@/hooks/useColorScheme";

interface CheckboxProps {
  value: boolean;
  onValueChange: (value: boolean) => void;
  style?: ViewStyle;
}

export function Checkbox({ value, onValueChange, style }: CheckboxProps) {
  const colorScheme = useColorScheme();

  return (
    <TouchableOpacity
      style={[
        styles.checkbox,
        {
          borderColor: value
            ? "#3b82f6"
            : colorScheme === "dark"
              ? "#4b5563"
              : "#d1d5db",
          backgroundColor: value ? "#3b82f6" : "transparent",
        },
        style,
      ]}
      onPress={() => onValueChange(!value)}
    >
      {value && <MaterialIcons name="check" size={12} color="white" />}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderRadius: 4,
    justifyContent: "center",
    alignItems: "center",
  },
});
