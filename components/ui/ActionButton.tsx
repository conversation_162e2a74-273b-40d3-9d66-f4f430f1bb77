import { MaterialIcons } from "@expo/vector-icons";
import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";

import { useColorScheme } from "@/hooks/useColorScheme";

interface ActionButtonProps {
  title: string;
  icon: string;
  onPress: () => void;
}

export function ActionButton({ title, icon, onPress }: ActionButtonProps) {
  const colorScheme = useColorScheme();

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: colorScheme === "dark" ? "#374151" : "#f3f4f6",
        },
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.iconContainer}>
        <MaterialIcons
          name={icon as any}
          size={28}
          color={colorScheme === "dark" ? "#e5e7eb" : "#4b5563"}
        />
      </View>
      <Text
        style={[
          styles.text,
          { color: colorScheme === "dark" ? "#e5e7eb" : "#1f2937" },
        ]}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 24,
    paddingVertical: 24,
    paddingHorizontal: 20,
    minHeight: 110,
    aspectRatio: 1.3,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  iconContainer: {
    marginBottom: 12,
  },
  text: {
    fontSize: 15,
    fontWeight: "600",
    textAlign: "center",
    letterSpacing: 0.3,
  },
});
