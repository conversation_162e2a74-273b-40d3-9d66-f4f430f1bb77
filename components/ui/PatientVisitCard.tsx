import { MaterialIcons } from "@expo/vector-icons";
import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";

import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";

interface PatientVisitCardProps {
  name: string;
  dateTime: string;
  hasNotification?: boolean;
  onPress?: () => void;
  onMenuPress?: () => void;
}

export function PatientVisitCard({
  name,
  dateTime,
  hasNotification = false,
  onPress,
  onMenuPress,
}: PatientVisitCardProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: colorScheme === "dark" ? "#1f2937" : "#ffffff",
          borderColor: colorScheme === "dark" ? "#374151" : "#e5e7eb",
        },
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.avatarContainer}>
        <MaterialIcons name="account-circle" size={40} color="#93c5fd" />
      </View>

      <View style={styles.contentContainer}>
        <Text style={[styles.name, { color: colors.text }]}>{name}</Text>
        <Text style={[styles.dateTime, { color: colors.text }]}>
          {dateTime}
        </Text>
      </View>

      {hasNotification && <View style={styles.notificationDot} />}

      <TouchableOpacity
        style={styles.menuButton}
        onPress={onMenuPress}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <MaterialIcons
          name="more-horiz"
          size={20}
          color={colorScheme === "dark" ? "#9ca3af" : "#6b7280"}
        />
      </TouchableOpacity>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    borderWidth: 1,
  },
  avatarContainer: {
    marginRight: 12,
  },
  contentContainer: {
    flex: 1,
  },
  name: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  dateTime: {
    fontSize: 14,
    opacity: 0.7,
  },
  notificationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#3b82f6",
    marginRight: 12,
  },
  menuButton: {
    padding: 4,
  },
});
