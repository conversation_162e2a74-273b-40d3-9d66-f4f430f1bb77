import { MaterialIcons } from "@expo/vector-icons";
import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetView,
} from "@gorhom/bottom-sheet";
import React, { forwardRef, useCallback, useMemo } from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";

import { ThemedText } from "@/components/ThemedText";
import { Colors } from "@/constants/Colors";
import { useLanguage } from "@/context/LanguageContext";
import { useColorScheme } from "@/hooks/useColorScheme";

interface LanguageSelectorProps {
  onClose?: () => void;
}

interface LanguageOption {
  id: "system" | "en" | "zh";
  title: string;
  subtitle: string;
}

export const LanguageSelector = forwardRef<BottomSheet, LanguageSelectorProps>(
  ({ onClose }, ref) => {
    const colorScheme = useColorScheme();
    const colors = Colors[colorScheme ?? "light"];
    const { locale, setLanguage, t, isSystemLanguage } = useLanguage();

    const snapPoints = useMemo(() => ["40%"], []);

    const languageOptions: LanguageOption[] = [
      {
        id: "system",
        title: t("profile.system"),
        subtitle: t("profile.systemSubtitle"),
      },
      {
        id: "en",
        title: "English",
        subtitle: t("profile.englishSubtitle"),
      },
      {
        id: "zh",
        title: "简体中文",
        subtitle: t("profile.chineseSubtitle"),
      },
    ];

    const currentLanguageId = isSystemLanguage ? "system" : locale;

    const handleLanguageSelect = useCallback(
      async (languageId: "system" | "en" | "zh") => {
        await setLanguage(languageId);
        onClose?.();
      },
      [setLanguage, onClose],
    );

    const renderBackdrop = useCallback(
      (props: any) => (
        <BottomSheetBackdrop
          {...props}
          disappearsOnIndex={-1}
          appearsOnIndex={0}
          opacity={0.5}
        />
      ),
      [],
    );

    return (
      <BottomSheet
        ref={ref}
        index={-1}
        snapPoints={snapPoints}
        enablePanDownToClose
        backdropComponent={renderBackdrop}
        backgroundStyle={{ backgroundColor: colors.card }}
        handleIndicatorStyle={{ backgroundColor: colors.icon }}
      >
        <BottomSheetView style={styles.contentContainer}>
          <ThemedText type="title" style={styles.title}>
            {t("profile.language")}
          </ThemedText>
          <ThemedText style={styles.subtitle}>
            {t("profile.selectLanguageMessage")}
          </ThemedText>

          <View style={styles.optionsContainer}>
            {languageOptions.map((option) => (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.optionItem,
                  { backgroundColor: colors.background },
                ]}
                onPress={() => handleLanguageSelect(option.id)}
              >
                <View style={styles.optionContent}>
                  <ThemedText type="subtitle" style={styles.optionTitle}>
                    {option.title}
                  </ThemedText>
                  <ThemedText style={styles.optionSubtitle}>
                    {option.subtitle}
                  </ThemedText>
                </View>
                {currentLanguageId === option.id && (
                  <MaterialIcons name="check" size={24} color={colors.tint} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </BottomSheetView>
      </BottomSheet>
    );
  },
);

LanguageSelector.displayName = "LanguageSelector";

const styles = StyleSheet.create({
  contentContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: "700",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 24,
  },
  optionsContainer: {
    gap: 12,
  },
  optionItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  optionSubtitle: {
    fontSize: 14,
    opacity: 0.7,
  },
});
