import { MaterialIcons } from "@expo/vector-icons";
import {
  BottomSheetBackdrop,
  BottomSheetModal,
  BottomSheetView,
} from "@gorhom/bottom-sheet";
import React, { forwardRef, useCallback, useMemo } from "react";
import {
  ActivityIndicator,
  Alert,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import Toast from "react-native-simple-toast";

import { ThemedText } from "@/components/ThemedText";
import { Colors } from "@/constants/Colors";
import { useTranslation } from "@/context/LanguageContext";
import { useColorScheme } from "@/hooks/useColorScheme";
import { useRegenerateCardFile } from "@/lib/api/hooks";

interface FileOptionsBottomSheetProps {
  cardId: string;
  fileId: string;
  fileStatus?: "pending" | "processing" | "completed" | "failed" | null;
  onReprocessSuccess?: () => void;
  onDismiss?: () => void;
}

export const FileOptionsBottomSheet = forwardRef<
  BottomSheetModal,
  FileOptionsBottomSheetProps
>(({ cardId, fileId, fileStatus, onReprocessSuccess, onDismiss }, ref) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const { t } = useTranslation();

  // Snap points for the bottom sheet
  const snapPoints = useMemo(() => ["35%"], []);

  // Regenerate card file mutation
  const regenerateFileMutation = useRegenerateCardFile(cardId);

  // Check if reprocess should be disabled
  const isReprocessDisabled = useMemo(() => {
    return (
      fileStatus === "pending" ||
      fileStatus === "processing" ||
      regenerateFileMutation.isPending
    );
  }, [fileStatus, regenerateFileMutation.isPending]);

  // Backdrop component
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    [],
  );

  const handleReprocessFile = useCallback(() => {
    Alert.alert(
      t("card.reprocessFileTitle") || "Reprocess File",
      t("card.reprocessFileMessage") ||
        "This will reprocess the file and remove the current results. Are you sure you want to continue?",
      [
        {
          text: t("common.cancel") || "Cancel",
          style: "cancel",
        },
        {
          text: t("card.reprocess") || "Reprocess",
          style: "destructive",
          onPress: async () => {
            try {
              await regenerateFileMutation.mutateAsync(fileId);
              if (ref && "current" in ref && ref.current) {
                ref.current.dismiss();
              }
              Toast.show(
                t("card.fileReprocessStarted") || "File reprocessing started",
                Toast.SHORT,
              );
              onReprocessSuccess?.();
            } catch {
              Toast.show(
                t("card.fileReprocessFailed") || "Failed to reprocess file",
                Toast.SHORT,
              );
            }
          },
        },
      ],
      { cancelable: true },
    );
  }, [fileId, regenerateFileMutation, t, ref, onReprocessSuccess]);

  return (
    <BottomSheetModal
      ref={ref}
      index={0}
      snapPoints={snapPoints}
      backdropComponent={renderBackdrop}
      backgroundStyle={{ backgroundColor: colors.card }}
      handleIndicatorStyle={{ backgroundColor: colors.icon }}
      enablePanDownToClose
      onDismiss={onDismiss}
    >
      <BottomSheetView style={styles.sheetContentContainer}>
        <ThemedText type="title" style={styles.sheetTitle}>
          {t("card.fileOptions") || "File Options"}
        </ThemedText>
        <ThemedText style={styles.sheetSubtitle}>
          {t("card.selectFileAction") || "Select an action for this file"}
        </ThemedText>

        <View style={styles.optionsContainer}>
          <TouchableOpacity
            style={[
              styles.optionItem,
              { backgroundColor: colors.background },
              isReprocessDisabled && styles.disabledOption,
            ]}
            onPress={handleReprocessFile}
            disabled={isReprocessDisabled}
          >
            <View style={styles.optionContent}>
              <View style={styles.optionHeader}>
                <MaterialIcons
                  name="refresh"
                  size={24}
                  color={
                    isReprocessDisabled ? colors.tabIconDefault : colors.tint
                  }
                  style={styles.optionIcon}
                />
                <ThemedText type="subtitle" style={styles.optionTitle}>
                  {t("card.reprocessFile") || "Reprocess File"}
                </ThemedText>
              </View>
              <ThemedText
                style={[
                  styles.optionDescription,
                  isReprocessDisabled && { opacity: 0.5 },
                ]}
              >
                {fileStatus === "pending" || fileStatus === "processing"
                  ? t("card.fileProcessing") || "File is currently processing"
                  : t("card.reprocessDescription") ||
                    "Regenerate analysis results"}
              </ThemedText>
            </View>
            {regenerateFileMutation.isPending && (
              <ActivityIndicator size="small" color={colors.tint} />
            )}
          </TouchableOpacity>
        </View>
      </BottomSheetView>
    </BottomSheetModal>
  );
});

FileOptionsBottomSheet.displayName = "FileOptionsBottomSheet";

const styles = StyleSheet.create({
  sheetContentContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  sheetTitle: {
    fontSize: 20,
    fontWeight: "700",
    marginBottom: 8,
  },
  sheetSubtitle: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 24,
  },
  optionsContainer: {
    gap: 12,
  },
  optionItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
  },
  optionContent: {
    flex: 1,
  },
  optionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  optionIcon: {
    marginRight: 12,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: "600",
  },
  optionDescription: {
    fontSize: 14,
    opacity: 0.7,
    marginLeft: 36,
  },
  disabledOption: {
    opacity: 0.6,
  },
});
