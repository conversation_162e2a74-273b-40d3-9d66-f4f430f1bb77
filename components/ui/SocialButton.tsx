import { MaterialIcons } from "@expo/vector-icons";
import React from "react";
import { Image, StyleSheet, TouchableOpacityProps } from "react-native";
import { Button } from "./Button";

interface SocialButtonProps extends TouchableOpacityProps {
  provider: "google" | "apple" | "wechat";
  title: string;
}

const providerConfig = {
  google: {
    icon: null,
    iconName: null,
    image: require("@/assets/images/google.png"),
  },
  apple: {
    icon: null,
    iconName: "apple.logo",
    image: null,
  },
  wechat: {
    icon: null,
    iconName: null,
    image: require("@/assets/images/wechat.png"),
  },
};

export function SocialButton({ provider, title, ...props }: SocialButtonProps) {
  const config = providerConfig[provider];

  const renderIcon = () => {
    if (config.image) {
      return <Image source={config.image} style={styles.image} />;
    } else if (config.iconName) {
      return (
        <MaterialIcons
          name={
            config.iconName === "apple.logo"
              ? ("apple" as any)
              : (config.iconName as any)
          }
          size={20}
          color="white"
        />
      );
    }
    return null;
  };

  return (
    <Button
      title={title}
      icon={renderIcon()}
      containerStyle={styles.button}
      {...props}
    />
  );
}

const styles = StyleSheet.create({
  button: {
    marginBottom: 12,
  },
  image: {
    width: 20,
    height: 20,
  },
});
