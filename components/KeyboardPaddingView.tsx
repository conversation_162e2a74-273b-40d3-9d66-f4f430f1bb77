import React from "react";
import { Platform } from "react-native";
import Animated, {
  useAnimatedKeyboard,
  useAnimatedStyle,
} from "react-native-reanimated";
import { useSafeAreaInsets } from "react-native-safe-area-context";

/**
 * Animated view that adds bottom padding equal to the visible keyboard height
 * (plus safe-area bottom inset) so that content isn't obscured when the
 * keyboard is open. Returns `null` on web.
 */
export const KeyboardPaddingView: React.FC =
  Platform.OS === "web"
    ? () => null
    : () => {
        const { height } = useAnimatedKeyboard();
        const { bottom } = useSafeAreaInsets();

        const animatedStyle = useAnimatedStyle(() => {
          return {
            height: Math.max(height.value, bottom),
          } as const;
        });

        return <Animated.View style={animatedStyle} />;
      };
